# Genesis Project Rules for AI Agents

## Project Info
- name: Genesis
- type: 2D Space Shooter Game
- language: TypeScript
- framework: Phaser.js v4
- build_tool: Vite
- package_manager: bun

## File Structure
- `src/`: Main source code
- `src/game/`: Core game logic
- `src/game/scenes/`: Game scenes
- `src/game/entities/`: Game entities (Player, Enemy, etc.)
- `src/game/systems/`: Core systems (Input, Physics, etc.)
- `public/assets/`: All game assets (images, audio)

## Architecture
- **Engine**: Phaser.js with Matter.js for physics.
- **Rendering**: Virtual resolution (320x240) scaled up to a target resolution.
- **Entities**: Class-based, extending `Phaser.Physics.Matter.Sprite`.
- **Systems**: Singletons for global access (e.g., `InputSystem.getInstance()`).
- **Communication**: Decoupled via a global `EventBus`.

## Scenes
- **Scene Flow**: `Boot` -> `Preloader` -> `LevelEditor` (default) or `Level`.
- `Level`: Main gameplay scene.
- `LevelEditor`: In-game tool to create and modify level data.
- `Debug`: An overlay scene for debugging, launched from `Level`.

## Entities
- **Player**: `src/game/entities/Player.ts` - User-controlled ship. Has two movement modes.
- **Enemy**: `src/game/entities/Enemy.ts` - AI-controlled ships.
- **Bullet**: `src/game/entities/Bullet.ts` - Projectiles, managed by `BulletManager`.
- **Module**: `src/game/entities/Module.ts` - Attachable ship components.

## Systems & Managers
- **InputSystem**: `src/game/systems/InputSystem.ts` - Handles keyboard and gamepad input.
- **BulletManager**: `src/game/systems/BulletManager.ts` - Manages bullet lifecycle and pooling.
- **ParticleManager**: `src/game/systems/ParticleManager.ts` - Creates particle effects like explosions.
- **ResolutionManager**: `src/game/systems/ResolutionManager.ts` - Manages coordinate transformations.
- **LevelDataManager**: `src/game/systems/LevelDataManager.ts` - Handles loading and saving of level data.
- **EventBus**: `src/game/systems/EventBus.ts` - Global event emitter for decoupled communication.

## Coordinate System
- **Virtual Resolution**: 320x240. All game logic should use these coordinates.
- **Target Resolution**: Multiple supported resolutions (e.g., 640x480, 1280x960).
- **Y-Axis**: In `LevelEditor`, Y=0 is at the bottom. In Phaser, Y=0 is at the top. Be mindful of conversions.
- **ResolutionManager**: Use this system to convert between virtual and screen coordinates.

## Events
- **Event Definitions**: `src/game/events.d.ts` - Augment `GenesisEventMap` to add new events.
- **Usage**: `import { emit, on } from '@/systems/EventBus';`
- **Example**: `emit('enemy:killed', { enemy });`

## Commands
- **Install dependencies**: `bun install <package name>`