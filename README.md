# Genesis Game

2D space shooter game built with Phaser 4 and Vite.

## Requirements

[Bun](https://bun.sh) is required to install dependencies and run scripts via `bun`.

## Available Commands

| Command | Description |
|---------|-------------|
| `bun install` | Install project dependencies |
| `bun run dev` | Launch a development web server |
| `bun run build` | Create a production build in the `dist` folder |
| `bun run dev-nolog` | Launch a development web server without sending anonymous data (see "About log.js" below) |
| `bun run build-nolog` | Create a production build in the `dist` folder without sending anonymous data (see "About log.js" below) |

## Writing Code

After cloning the repo, run `bun install` from your project directory. Then, you can start the local development server by running `bun run dev`.

The local development server runs on `http://localhost:8080` by default. Please see the Vite documentation if you wish to change this, or add SSL support.

Once the server is running you can edit any of the files in the `src` folder. Vite will automatically recompile your code and then reload the browser.