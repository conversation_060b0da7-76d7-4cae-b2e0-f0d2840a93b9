/**
 * Level Data Types for Genesis Level Editor
 * 
 * Defines the data structures for storing level information including
 * background tiles, structures, and entities using our coordinate system
 * where Y=0 is at the bottom and Y increases upward.
 */

// Base interface for tile coordinates
export interface TileCoordinate {
  x: number;
  y: number;
}

// Background layer - static tilemap images/sprites
export interface BackgroundTile {
  x: number;
  y: number;
  tileId: string; // Reference to tile asset
  variant?: number; // Optional variant for tile variations
}

// Structures layer - static structural elements
export interface StructureTile {
  x: number;
  y: number;
  structureType: string; // Type of structure
  variant?: number; // Optional variant
  properties?: Record<string, any>; // Extensible properties
}

// Entities layer - dynamic game objects
export interface EntityTile {
  x: number;
  y: number;
  type: string; // Entity type (initially "Enemy")
  tier: number; // Difficulty/variant tier
  properties?: Record<string, any>; // Extensible properties for future use
}

// Level metadata
export interface LevelMetadata {
  name: string;
  version: string;
  created: Date;
  modified: Date;
  author?: string;
  description?: string;
  bounds?: {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  };
}

// Main level data structure
export interface LevelData {
  metadata: LevelMetadata;
  background: BackgroundTile[];
  structures: StructureTile[];
  entities: EntityTile[];
}

// Helper type for layer names
export type LayerType = 'background' | 'structures' | 'entities';

// Helper type for tile data by layer
export type TileData = BackgroundTile | StructureTile | EntityTile;

// Factory functions for creating new tiles
export const createBackgroundTile = (x: number, y: number, tileId: string, variant?: number): BackgroundTile => ({
  x,
  y,
  tileId,
  variant,
});

export const createStructureTile = (x: number, y: number, structureType: string, variant?: number, properties?: Record<string, any>): StructureTile => ({
  x,
  y,
  structureType,
  variant,
  properties,
});

export const createEntityTile = (x: number, y: number, type: string, tier: number, properties?: Record<string, any>): EntityTile => ({
  x,
  y,
  type,
  tier,
  properties,
});

// Factory function for creating empty level data
export const createEmptyLevel = (name: string = "Untitled Level"): LevelData => ({
  metadata: {
    name,
    version: "1.0.0",
    created: new Date(),
    modified: new Date(),
    author: "Level Editor",
    description: "",
  },
  background: [],
  structures: [],
  entities: [],
});
