import { Boot } from "./scenes/Boot";
import { GameOver } from "./scenes/GameOver";
import { Level } from "./scenes/Level";
import { Hub } from "./scenes/Hub";
import { AUTO, Game } from "phaser";
import { Preloader } from "./scenes/Preloader";
import { Debug } from "./scenes/Debug";
import { LevelEditor } from "./scenes/LevelEditor";
import { GameConfig } from "./config/GameConfig";

// Initialize game configuration
const gameConfig = GameConfig.getInstance();
const resolution = gameConfig.getResolution();

// Find out more information about the Game Config at:
// https://docs.phaser.io/api-documentation/typedef/types-core#gameconfig
const config: Phaser.Types.Core.GameConfig = {
  type: AUTO,
  width: resolution.targetWidth,
  height: resolution.targetHeight,
  parent: "game-container",
  backgroundColor: "#000000",
  scene: [Boot, Preloader, Hub, Level, GameOver, Debug, LevelEditor],
  physics: {
    default: "matter",
    matter: {
      enableSleeping: false,
      gravity: {
        x: 0,
        y: 0,
      },
      debug: {
        showBody: false,
        showStaticBody: false,
        showVelocity: false,
        showCollisions: false,
      },
    },
  },
  input: {
    gamepad: true,
  },
  scale: {
    mode: Phaser.Scale.NONE, // We handle scaling manually for pixel-perfect results
    autoCenter: Phaser.Scale.CENTER_BOTH,
  },
  render: {
    pixelArt: true, // Enable pixel-perfect rendering
    antialias: false, // Disable antialiasing for crisp pixels
    roundPixels: true, // Round pixel positions to avoid sub-pixel rendering
  },
};

const StartGame = (parent: string) => {
  return new Game({ ...config, parent });
};

export default StartGame;
