/*
 * InputSystem Usage Examples
 *
 * This file demonstrates various ways to use the InputSystem in your Phaser game.
 * Import and use the InputSystem in your scenes for unified input handling.
 */

import { Scene } from "phaser";
import { InputSystem, InputConfig, GamepadCallbacks } from "./InputSystem";

export class InputSystemExample extends Scene {
  private inputSystem: InputSystem;

  constructor() {
    super("InputSystemExample");
  }

  create() {
    // Get the singleton instance of the input system
    this.inputSystem = InputSystem.getInstance();

    // Initialize the input system with optional gamepad callbacks
    const gamepadCallbacks: GamepadCallbacks = {
      onConnect: (gamepad) => {
        console.log(`Gamepad connected: ${gamepad.id}`);
        // Show UI notification, update game state, etc.
      },
      onDisconnect: (gamepad) => {
        console.log(`Gamepad disconnected: ${gamepad.id}`);
        // Handle gamepad disconnection
      },
    };

    this.inputSystem.initialize(this, gamepadCallbacks);

    // Example 1: Custom input mapping
    this.customizeInputMapping();

    // Example 2: Using input system callbacks
    this.setupGamepadCallbacks();
  }

  update() {
    // IMPORTANT: Update the input system for proper button state tracking
    this.inputSystem.update();

    // Example 3: Basic movement input
    this.handleMovement();

    // Example 4: Action button handling
    this.handleActions();

    // Example 5: Advanced input checking
    this.advancedInputHandling();

    // Example 6: Gamepad-specific features
    this.handleGamepadFeatures();
  }

  // Example 1: Customizing input mappings
  private customizeInputMapping(): void {
    const customConfig: Partial<InputConfig> = {
      // Customize movement keys
      up: {
        keyboard: ["UP", "W", "I"], // Add 'I' key for up
        gamepad: [12], // D-pad up
      },
      // Add a new custom action
      jump: {
        keyboard: ["SPACE", "UP"],
        gamepad: [0, 3], // A button and Y button
      },
      // Customize action buttons
      action1: {
        keyboard: ["Z", "J", "ENTER"],
        gamepad: [0], // A button
      },
      action2: {
        keyboard: ["X", "K"],
        gamepad: [1], // B button
      },
      // Add menu/pause functionality
      pause: {
        keyboard: ["ESC", "P"],
        gamepad: [9], // Start button
      },
    };

    this.inputSystem.updateInputConfig(customConfig);
  }

  // Example 2: Setting up gamepad callbacks
  private setupGamepadCallbacks(): void {
    const callbacks: GamepadCallbacks = {
      onConnect: (gamepad) => {
        // Show in-game notification
        this.showNotification(`Controller connected: ${gamepad.id}`);

        // Update UI to show gamepad prompts instead of keyboard prompts
        this.updateUIForGamepad(true);

        // Vibrate gamepad if supported (Phaser doesn't have built-in vibration,
        // but you could use the Web Gamepad API directly)
        console.log("Gamepad connected:", gamepad);
      },

      onDisconnect: (gamepad) => {
        this.showNotification("Controller disconnected");
        this.updateUIForGamepad(false);
      },
    };

    this.inputSystem.setGamepadCallbacks(callbacks);
  }

  // Example 3: Handling movement input
  private handleMovement(): void {
    // Get normalized movement vector (-1 to 1 for each axis)
    const movement = this.inputSystem.getMovementVector();
    const speed = 200;

    // Apply movement to a game object (assuming you have a player object)
    if (movement.x !== 0 || movement.y !== 0) {
      // Apply movement with frame-rate independent speed
      // this.player.x += movement.x * speed * this.game.loop.delta / 1000;
      // this.player.y += movement.y * speed * this.game.loop.delta / 1000;

      console.log(
        `Moving: x=${movement.x.toFixed(2)}, y=${movement.y.toFixed(2)}`
      );
    }

    // Get raw movement vector (not normalized, useful for digital-only input)
    const rawMovement = this.inputSystem.getRawMovementVector();
    if (rawMovement.x !== 0 || rawMovement.y !== 0) {
      console.log(`Raw movement: x=${rawMovement.x}, y=${rawMovement.y}`);
    }
  }

  // Example 4: Handling action buttons
  private handleActions(): void {
    // Check if action button is currently pressed
    if (this.inputSystem.isDown("action1")) {
      console.log("Action1 is being held down");
      // Could be used for charging attacks, continuous actions, etc.
    }

    // Check if action button was just pressed this frame
    if (this.inputSystem.pressed("action1")) {
      console.log("Action1 was just pressed");
      // Use for single-press actions like jumping, shooting, etc.
      this.performAction("primary");
    }

    // Handle multiple action buttons
    if (this.inputSystem.pressed("action2")) {
      this.performAction("secondary");
    }

    // Handle pause/menu
    if (this.inputSystem.pressed("pause")) {
      this.togglePause();
    }

    // Custom action (if you added it in customizeInputMapping)
    if (this.inputSystem.pressed("jump")) {
      this.performJump();
    }
  }

  // Example 5: Advanced input handling
  private advancedInputHandling(): void {
    // Check gamepad status
    if (this.inputSystem.isGamepadActive()) {
      const gamepad = this.inputSystem.getActiveGamepad();
      if (gamepad) {
        console.log(`Using gamepad: ${gamepad.id}`);

        // You can access the raw Phaser gamepad object for advanced features
        // Check analog stick positions directly
        if (gamepad.leftStick) {
          const stickX = gamepad.leftStick.x;
          const stickY = gamepad.leftStick.y;
          console.log(`Left stick: ${stickX.toFixed(2)}, ${stickY.toFixed(2)}`);
        }

        // Check right stick for camera control
        if (gamepad.rightStick) {
          const cameraX = gamepad.rightStick.x;
          const cameraY = gamepad.rightStick.y;
          // Apply to camera movement
          // this.cameras.main.scrollX += cameraX * 5;
          // this.cameras.main.scrollY += cameraY * 5;
        }
      }
    }

    // Get all mapped actions
    const allActions = this.inputSystem.getAllMappedActions();
    console.log("Available actions:", allActions);

    // Check specific action mapping
    const action1Mapping = this.inputSystem.getActionMapping("action1");
    if (action1Mapping) {
      console.log("Action1 mapped to:", action1Mapping);
    }
  }

  // Example 6: Gamepad-specific features
  private handleGamepadFeatures(): void {
    if (!this.inputSystem.isGamepadActive()) return;

    const gamepad = this.inputSystem.getActiveGamepad()!;

    // Check shoulder buttons (triggers/bumpers)
    if (gamepad.L1) {
      console.log("Left bumper pressed");
    }

    if (gamepad.R1) {
      console.log("Right bumper pressed");
    }

    // Check trigger values (analog)
    if (gamepad.L2 > 0.1) {
      console.log(`Left trigger: ${gamepad.L2.toFixed(2)}`);
    }

    if (gamepad.R2 > 0.1) {
      console.log(`Right trigger: ${gamepad.R2.toFixed(2)}`);
    }

    // Handle D-pad separately from movement if needed
    if (gamepad.up) console.log("D-pad up");
    if (gamepad.down) console.log("D-pad down");
    if (gamepad.left) console.log("D-pad left");
    if (gamepad.right) console.log("D-pad right");

    // Face buttons
    if (gamepad.A) console.log("A button (or X on PlayStation)");
    if (gamepad.B) console.log("B button (or Circle on PlayStation)");
    if (gamepad.X) console.log("X button (or Square on PlayStation)");
    if (gamepad.Y) console.log("Y button (or Triangle on PlayStation)");
  }

  // Helper methods for the examples
  private performAction(type: "primary" | "secondary"): void {
    console.log(`Performing ${type} action`);
    // Implement your action logic here
  }

  private performJump(): void {
    console.log("Jumping!");
    // Implement jump logic
  }

  private togglePause(): void {
    console.log("Toggling pause");
    // Implement pause logic
    // this.scene.pause();
  }

  private showNotification(message: string): void {
    console.log(`Notification: ${message}`);
    // Implement in-game notification system
  }

  private updateUIForGamepad(useGamepad: boolean): void {
    console.log(`Updating UI for ${useGamepad ? "gamepad" : "keyboard"}`);
    // Update button prompts, tutorials, etc.
  }

  shutdown(): void {
    // Clean up when scene ends
    this.inputSystem.destroy();
  }
}

/*
 * Quick Reference:
 *
 * Basic Usage:
 * 1. Get instance: InputSystem.getInstance()
 * 2. Initialize: inputSystem.initialize(scene, callbacks?)
 * 3. Update: inputSystem.update() // Call this in your scene's update() method!
 * 4. Check input: inputSystem.isDown('action'), inputSystem.justPressed('action'), inputSystem.justReleased('action')
 * 5. Get movement: inputSystem.getMovementVector()
 * 6. Clean up: inputSystem.destroy()
 *
 * Default Actions Available:
 * - Movement: 'up', 'down', 'left', 'right'
 * - Actions: 'action1', 'action2', 'action3', 'action4'
 * - System: 'start', 'select'
 *
 * Default Keyboard Mappings:
 * - Movement: Arrow keys, WASD
 * - Action1: Space, Z, J
 * - Action2: X, K
 * - Action3: C, L
 * - Action4: V, I
 * - Start: Enter
 * - Select: Shift
 *
 * Default Gamepad Mappings:
 * - Movement: D-pad, Left analog stick
 * - Action1: A button (Xbox) / X button (PlayStation)
 * - Action2: B button (Xbox) / Circle button (PlayStation)
 * - Action3: X button (Xbox) / Square button (PlayStation)
 * - Action4: Y button (Xbox) / Triangle button (PlayStation)
 * - Start: Start button
 * - Select: Select/Back button
 */
