/**
 * LevelDataManager
 *
 * Manages level data operations for the Genesis Level Editor.
 * Provides efficient methods for getting/setting tile data across all layers
 * using sparse data structures for performance with large levels.
 */

import {
  LevelData,
  LayerType,
  TileData,
  BackgroundTile,
  StructureTile,
  EntityTile,
  TileCoordinate,
  createEmptyLevel,
  createBackgroundTile,
  createStructureTile,
  createEntityTile,
} from "../types/LevelData";

export class LevelDataManager {
  private levelData: LevelData;

  // Sparse data structures for efficient lookups
  // Using coordinate string as key: "x,y"
  private backgroundMap: Map<string, BackgroundTile> = new Map();
  private structuresMap: Map<string, StructureTile> = new Map();
  private entitiesMap: Map<string, EntityTile> = new Map();

  constructor(initialData?: LevelData) {
    this.levelData = initialData || createEmptyLevel();
    this.rebuildMaps();
  }

  // Helper method to convert coordinates to string key
  private coordToKey(x: number, y: number): string {
    return `${x},${y}`;
  }

  // Helper method to convert string key back to coordinates
  private keyToCoord(key: string): TileCoordinate {
    const [x, y] = key.split(",").map(Number);
    return { x, y };
  }

  // Rebuild internal maps from level data arrays
  private rebuildMaps(): void {
    this.backgroundMap.clear();
    this.structuresMap.clear();
    this.entitiesMap.clear();

    // Populate background map
    for (const tile of this.levelData.background) {
      const key = this.coordToKey(tile.x, tile.y);
      this.backgroundMap.set(key, tile);
    }

    // Populate structures map
    for (const tile of this.levelData.structures) {
      const key = this.coordToKey(tile.x, tile.y);
      this.structuresMap.set(key, tile);
    }

    // Populate entities map
    for (const tile of this.levelData.entities) {
      const key = this.coordToKey(tile.x, tile.y);
      this.entitiesMap.set(key, tile);
    }
  }

  // Sync maps back to arrays (call before serializing)
  private syncMapsToArrays(): void {
    this.levelData.background = Array.from(this.backgroundMap.values());
    this.levelData.structures = Array.from(this.structuresMap.values());
    this.levelData.entities = Array.from(this.entitiesMap.values());
    this.levelData.metadata.modified = new Date();
  }

  // Get tile data for a specific coordinate and layer
  public getTile(x: number, y: number, layer: LayerType): TileData | null {
    const key = this.coordToKey(x, y);

    switch (layer) {
      case "background":
        return this.backgroundMap.get(key) || null;
      case "structures":
        return this.structuresMap.get(key) || null;
      case "entities":
        return this.entitiesMap.get(key) || null;
      default:
        return null;
    }
  }

  // Set background tile
  public setBackgroundTile(
    x: number,
    y: number,
    tileId: string,
    variant?: number
  ): void {
    const key = this.coordToKey(x, y);
    const tile = createBackgroundTile(x, y, tileId, variant);
    this.backgroundMap.set(key, tile);
  }

  // Set structure tile
  public setStructureTile(
    x: number,
    y: number,
    structureType: string,
    variant?: number,
    properties?: Record<string, any>
  ): void {
    const key = this.coordToKey(x, y);
    const tile = createStructureTile(x, y, structureType, variant, properties);
    this.structuresMap.set(key, tile);
  }

  // Set entity tile
  public setEntityTile(
    x: number,
    y: number,
    type: string,
    tier: number,
    properties?: Record<string, any>
  ): void {
    const key = this.coordToKey(x, y);
    const tile = createEntityTile(x, y, type, tier, properties);
    this.entitiesMap.set(key, tile);
  }

  // Remove tile from specific layer
  public removeTile(x: number, y: number, layer: LayerType): boolean {
    const key = this.coordToKey(x, y);

    switch (layer) {
      case "background":
        return this.backgroundMap.delete(key);
      case "structures":
        return this.structuresMap.delete(key);
      case "entities":
        return this.entitiesMap.delete(key);
      default:
        return false;
    }
  }

  // Check if a tile has any data in any layer
  public hasTileData(x: number, y: number): boolean {
    const key = this.coordToKey(x, y);
    return (
      this.backgroundMap.has(key) ||
      this.structuresMap.has(key) ||
      this.entitiesMap.has(key)
    );
  }

  // Get all tiles in a specific layer
  public getLayerTiles(layer: LayerType): TileData[] {
    switch (layer) {
      case "background":
        return Array.from(this.backgroundMap.values());
      case "structures":
        return Array.from(this.structuresMap.values());
      case "entities":
        return Array.from(this.entitiesMap.values());
      default:
        return [];
    }
  }

  // Get tiles in a rectangular area
  public getTilesInArea(
    minX: number,
    minY: number,
    maxX: number,
    maxY: number,
    layer?: LayerType
  ): TileData[] {
    const tiles: TileData[] = [];
    const layers: LayerType[] = layer
      ? [layer]
      : ["background", "structures", "entities"];

    for (const layerType of layers) {
      for (let x = minX; x <= maxX; x++) {
        for (let y = minY; y <= maxY; y++) {
          const tile = this.getTile(x, y, layerType);
          if (tile) {
            tiles.push(tile);
          }
        }
      }
    }

    return tiles;
  }

  // Clear all data in a specific layer
  public clearLayer(layer: LayerType): void {
    switch (layer) {
      case "background":
        this.backgroundMap.clear();
        break;
      case "structures":
        this.structuresMap.clear();
        break;
      case "entities":
        this.entitiesMap.clear();
        break;
    }
  }

  // Clear all data
  public clearAll(): void {
    this.backgroundMap.clear();
    this.structuresMap.clear();
    this.entitiesMap.clear();
  }

  // Get level bounds (min/max coordinates with data)
  public getLevelBounds(): {
    minX: number;
    maxX: number;
    minY: number;
    maxY: number;
  } | null {
    const allCoords: TileCoordinate[] = [];

    // Collect all coordinates from all layers
    for (const key of this.backgroundMap.keys()) {
      allCoords.push(this.keyToCoord(key));
    }
    for (const key of this.structuresMap.keys()) {
      allCoords.push(this.keyToCoord(key));
    }
    for (const key of this.entitiesMap.keys()) {
      allCoords.push(this.keyToCoord(key));
    }

    if (allCoords.length === 0) {
      return null;
    }

    const xs = allCoords.map((coord) => coord.x);
    const ys = allCoords.map((coord) => coord.y);

    return {
      minX: Math.min(...xs),
      maxX: Math.max(...xs),
      minY: Math.min(...ys),
      maxY: Math.max(...ys),
    };
  }

  // Get current level data (syncs maps to arrays first)
  public getLevelData(): LevelData {
    this.syncMapsToArrays();
    return { ...this.levelData };
  }

  // Load new level data
  public loadLevelData(data: LevelData): void {
    this.levelData = { ...data };
    this.rebuildMaps();
  }

  // Get level metadata
  public getMetadata() {
    return { ...this.levelData.metadata };
  }

  // Update level metadata
  public updateMetadata(
    updates: Partial<typeof this.levelData.metadata>
  ): void {
    this.levelData.metadata = { ...this.levelData.metadata, ...updates };
    this.levelData.metadata.modified = new Date();
  }

  // Get statistics about the level
  public getStats() {
    return {
      backgroundTiles: this.backgroundMap.size,
      structureTiles: this.structuresMap.size,
      entityTiles: this.entitiesMap.size,
      totalTiles:
        this.backgroundMap.size +
        this.structuresMap.size +
        this.entitiesMap.size,
      bounds: this.getLevelBounds(),
    };
  }
}
