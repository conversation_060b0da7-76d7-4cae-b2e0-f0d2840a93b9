/**
 * EventBus.ts
 * Global, HMR-safe event bus for decoupled communication between scenes/systems.
 *
 * - Built on Phaser.Events.EventEmitter.
 * - Works untyped by default, but supports optional strong typing via declaration merging:
 *
 *   // anywhere in your codebase, augment the global GenesisEventMap
 *   declare global {
 *     interface GenesisEventMap {
 *       PLAYER_DAMAGED: { hp: number; maxHp: number };
 *       CURRENCY_CHANGED: { amount: number; delta: number };
 *       OPEN_DIALOGUE: { id: string; node?: string };
 *     }
 *   }
 *
 * - Convenience:
 *   - EventBus.waitFor('EVENT', 5000) -> Promise resolves with payload or rejects on timeout.
 *   - const scope = EventBus.createScope(); scope.on('X', fn); scope.clear() cleans all.
 * - HMR-safe: Singleton instance persists across module reloads.
 */

import Phaser from "phaser";

// ---- Optional typing via declaration merging --------------------------------
// By default, this interface is empty. You can augment it in your project
// to get end-to-end type safety for event names and payloads.
declare global {
  interface GenesisEventMap {} // eslint-disable-line @typescript-eslint/no-empty-interface
}

// If the map is not augmented, keyof GenesisEventMap is never, so we fall back to string.
type EventKey = keyof GenesisEventMap extends never
  ? string
  : Extract<keyof GenesisEventMap, string>;

type Payload<K extends EventKey> = K extends keyof GenesisEventMap
  ? GenesisEventMap[K]
  : unknown;

type Listener<K extends EventKey> = (payload: Payload<K>) => void;

// -----------------------------------------------------------------------------

class TypedEventBus extends Phaser.Events.EventEmitter {
  private _logging = false;

  /** Enable/disable console logging of emitted events (dev aid). */
  setLoggingEnabled(enabled: boolean) {
    this._logging = enabled;
  }

  /** Add listener (typed if you augmented GenesisEventMap). */
  on<K extends EventKey>(event: K, fn: Listener<K>, context?: unknown): this {
    return super.on(event as string, fn as (...args: any[]) => void, context);
  }

  /** Add once-listener (typed). */
  once<K extends EventKey>(event: K, fn: Listener<K>, context?: unknown): this {
    return super.once(event as string, fn as (...args: any[]) => void, context);
  }

  /** Remove listener (typed). */
  off<K extends EventKey>(
    event: K,
    fn: Listener<K>,
    context?: unknown,
    once?: boolean
  ): this {
    return super.off(
      event as string,
      fn as (...args: any[]) => void,
      context,
      once
    );
  }

  /** Emit event with optional payload (typed). Returns true if any listeners handled it. */
  emit<K extends EventKey>(event: K, payload?: Payload<K>): boolean {
    if (this._logging) {
      // eslint-disable-next-line no-console
      console.debug(`[EventBus] ${String(event)}`, payload);
    }
    return super.emit(event as string, payload);
  }

  /** Await a single occurrence of an event. Optional timeout (ms). */
  waitFor<K extends EventKey>(
    event: K,
    timeoutMs?: number
  ): Promise<Payload<K>> {
    return new Promise<Payload<K>>((resolve, reject) => {
      let timer: number | undefined;

      const handler = (payload: Payload<K>) => {
        if (timer !== undefined) {
          window.clearTimeout(timer);
        }
        resolve(payload);
      };

      this.once(event, handler);

      if (typeof timeoutMs === "number" && timeoutMs > 0) {
        timer = window.setTimeout(() => {
          this.off(event, handler);
          reject(
            new Error(`EventBus.waitFor timeout waiting for "${String(event)}"`)
          );
        }, timeoutMs);
      }
    });
  }

  /**
   * Create a listener scope that you can clear in Scene.shutdown/destroy().
   * Usage:
   *   const scope = EventBus.createScope();
   *   scope.on('PLAYER_DAMAGED', ({ hp }) => {...});
   *   // later
   *   scope.clear();
   */
  createScope() {
    const bus = this;
    const bindings: Array<{
      event: EventKey;
      fn: Listener<any>;
      context?: unknown;
    }> = [];

    return {
      on<K extends EventKey>(event: K, fn: Listener<K>, context?: unknown) {
        bus.on(event, fn, context);
        bindings.push({ event, fn, context });
        return this;
      },
      once<K extends EventKey>(event: K, fn: Listener<K>, context?: unknown) {
        // we still track it in case user clears before it fires
        bus.once(event, fn, context);
        bindings.push({ event, fn, context });
        return this;
      },
      clear() {
        for (const b of bindings) {
          bus.off(b.event as any, b.fn as any, b.context);
        }
        bindings.length = 0;
      },
    };
  }
}

// ---- HMR-safe singleton ------------------------------------------------------

declare global {
  // Attach to globalThis so we don't duplicate across HMR reloads.
  // Using a symbol reduces collision risk.
  // eslint-disable-next-line no-var
  var __GENESIS_EVENT_BUS__: TypedEventBus | undefined;
}

const g = globalThis as unknown as { __GENESIS_EVENT_BUS__?: TypedEventBus };

if (!g.__GENESIS_EVENT_BUS__) {
  g.__GENESIS_EVENT_BUS__ = new TypedEventBus();
}

/**
 * Export the singleton instance.
 * Import this and use: EventBus.on('EVENT', cb); EventBus.emit('EVENT', payload);
 */
const EventBus = g.__GENESIS_EVENT_BUS__!;

// Optionally enable logging in dev via query param or manual toggle
if (typeof window !== "undefined") {
  const params = new URLSearchParams(window.location.search);
  if (params.get("logEvents") === "1") {
    EventBus.setLoggingEnabled(true);
  }
}

export default EventBus;

// Also export a couple of convenience bound functions if you prefer functional style.
export const on = EventBus.on.bind(EventBus) as <K extends EventKey>(
  event: K,
  fn: Listener<K>,
  context?: unknown
) => TypedEventBus;

export const once = EventBus.once.bind(EventBus) as <K extends EventKey>(
  event: K,
  fn: Listener<K>,
  context?: unknown
) => TypedEventBus;

export const off = EventBus.off.bind(EventBus) as <K extends EventKey>(
  event: K,
  fn: Listener<K>,
  context?: unknown,
  once?: boolean
) => TypedEventBus;

export const emit = EventBus.emit.bind(EventBus) as <K extends EventKey>(
  event: K,
  payload?: Payload<K>
) => boolean;

export const waitFor = EventBus.waitFor.bind(EventBus) as <K extends EventKey>(
  event: K,
  timeoutMs?: number
) => Promise<Payload<K>>;
