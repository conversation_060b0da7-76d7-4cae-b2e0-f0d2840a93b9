/**
 * ResolutionManager.ts
 * Handles coordinate transformations between virtual and target resolutions
 * Provides utilities for pixel-perfect scaling and smooth sub-pixel movement
 */

import { GameConfig } from "../config/GameConfig";

export interface VirtualPoint {
  x: number;
  y: number;
}

export interface ScreenPoint {
  x: number;
  y: number;
}

export interface VirtualBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ScreenBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

class ResolutionManager {
  private static instance: ResolutionManager;
  private gameConfig: GameConfig;

  private constructor() {
    this.gameConfig = GameConfig.getInstance();
  }

  public static getInstance(): ResolutionManager {
    if (!ResolutionManager.instance) {
      ResolutionManager.instance = new ResolutionManager();
    }
    return ResolutionManager.instance;
  }

  /**
   * Convert virtual coordinates to screen coordinates
   */
  public virtualToScreen(virtualPoint: VirtualPoint): ScreenPoint {
    return {
      x:
        (virtualPoint.x * this.gameConfig.getTargetWidth()) /
        this.gameConfig.getVirtualWidth(),
      y:
        (virtualPoint.y * this.gameConfig.getTargetHeight()) /
        this.gameConfig.getVirtualHeight(),
    };
  }

  /**
   * Convert screen coordinates to virtual coordinates
   */
  public screenToVirtual(screenPoint: ScreenPoint): VirtualPoint {
    return {
      x:
        (screenPoint.x / this.gameConfig.getTargetWidth()) *
        this.gameConfig.getVirtualWidth(),
      y:
        (screenPoint.y / this.gameConfig.getTargetHeight()) *
        this.gameConfig.getVirtualHeight(),
    };
  }

  /**
   * Convert virtual bounds to screen bounds
   */
  public virtualBoundsToScreen(virtualBounds: VirtualBounds): ScreenBounds {
    const scaleFactor = this.gameConfig.getScaleFactor();
    return {
      x: virtualBounds.x * scaleFactor,
      y: virtualBounds.y * scaleFactor,
      width: virtualBounds.width * scaleFactor,
      height: virtualBounds.height * scaleFactor,
    };
  }

  /**
   * Convert screen bounds to virtual bounds
   */
  public screenBoundsToVirtual(screenBounds: ScreenBounds): VirtualBounds {
    const scaleFactor = this.gameConfig.getScaleFactor();
    return {
      x: screenBounds.x / scaleFactor,
      y: screenBounds.y / scaleFactor,
      width: screenBounds.width / scaleFactor,
      height: screenBounds.height / scaleFactor,
    };
  }

  /**
   * Scale a virtual value to screen value
   */
  public scaleValue(virtualValue: number): number {
    return virtualValue * this.gameConfig.getScaleFactor();
  }

  /**
   * Unscale a screen value to virtual value
   */
  public unscaleValue(screenValue: number): number {
    return screenValue / this.gameConfig.getScaleFactor();
  }

  /**
   * Get the current virtual resolution
   */
  public getVirtualResolution(): { width: number; height: number } {
    return {
      width: this.gameConfig.getVirtualWidth(),
      height: this.gameConfig.getVirtualHeight(),
    };
  }

  /**
   * Get the current target resolution
   */
  public getTargetResolution(): { width: number; height: number } {
    return {
      width: this.gameConfig.getTargetWidth(),
      height: this.gameConfig.getTargetHeight(),
    };
  }

  /**
   * Get the current scale factor
   */
  public getScaleFactor(): number {
    return this.gameConfig.getScaleFactor();
  }

  /**
   * Check if a virtual point is within virtual bounds
   */
  public isVirtualPointInBounds(
    point: VirtualPoint,
    bounds: VirtualBounds
  ): boolean {
    return (
      point.x >= bounds.x &&
      point.x <= bounds.x + bounds.width &&
      point.y >= bounds.y &&
      point.y <= bounds.y + bounds.height
    );
  }

  /**
   * Clamp virtual coordinates to virtual world bounds
   */
  public clampVirtualPoint(
    point: VirtualPoint,
    bounds: VirtualBounds
  ): VirtualPoint {
    return {
      x: Math.max(bounds.x, Math.min(bounds.x + bounds.width, point.x)),
      y: Math.max(bounds.y, Math.min(bounds.y + bounds.height, point.y)),
    };
  }

  /**
   * Get smooth movement step size for animations
   * This ensures smooth movement at higher resolutions by utilizing sub-pixel precision
   */
  public getSmoothStepSize(virtualDistance: number, duration: number): number {
    const scaleFactor = this.gameConfig.getScaleFactor();
    // At higher scale factors, we can have more granular movement steps
    // while maintaining the same overall speed
    return virtualDistance / (duration * scaleFactor);
  }

  /**
   * Convert virtual velocity to screen velocity for smooth movement
   */
  public virtualVelocityToScreen(virtualVelocity: VirtualPoint): ScreenPoint {
    const scaleFactor = this.gameConfig.getScaleFactor();
    return {
      x: virtualVelocity.x * scaleFactor,
      y: virtualVelocity.y * scaleFactor,
    };
  }

  /**
   * Convert screen velocity to virtual velocity
   */
  public screenVelocityToVirtual(screenVelocity: ScreenPoint): VirtualPoint {
    const scaleFactor = this.gameConfig.getScaleFactor();
    return {
      x: screenVelocity.x / scaleFactor,
      y: screenVelocity.y / scaleFactor,
    };
  }

  /**
   * Get virtual world bounds (0, 0, virtualWidth, virtualHeight)
   */
  public getVirtualWorldBounds(): VirtualBounds {
    return {
      x: 0,
      y: 0,
      width: this.gameConfig.getVirtualWidth(),
      height: this.gameConfig.getVirtualHeight(),
    };
  }

  /**
   * Get screen world bounds
   */
  public getScreenWorldBounds(): ScreenBounds {
    return {
      x: 0,
      y: 0,
      width: this.gameConfig.getTargetWidth(),
      height: this.gameConfig.getTargetHeight(),
    };
  }
}

export { ResolutionManager };
