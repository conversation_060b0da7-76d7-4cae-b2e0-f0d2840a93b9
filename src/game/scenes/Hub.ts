import { Scene } from "phaser";
import { Player, MovementMode } from "../entities/Player";
import { InputSystem } from "../systems/InputSystem";
import { ResolutionManager } from "../systems/ResolutionManager";
import { Teleport } from "../entities/Teleport";

class Hub extends Scene {
  private player!: Player;
  private bulletManager!: BulletManager;
  private inputSystem!: InputSystem;
  private resolutionManager!: ResolutionManager;
  private teleport!: Teleport;

  // Hub world size will be calculated based on target resolution
  private HUB_WORLD_WIDTH!: number;
  private HUB_WORLD_HEIGHT!: number;

  constructor() {
    super("Hub");
  }

  create(): void {
    // Initialize systems
    this.resolutionManager = ResolutionManager.getInstance();
    this.inputSystem = InputSystem.getInstance();
    this.inputSystem.initialize(this);

    // Calculate Hub world size (3x the target resolution)
    const targetRes = this.resolutionManager.getTargetResolution();
    this.HUB_WORLD_WIDTH = targetRes.width * 3;
    this.HUB_WORLD_HEIGHT = targetRes.height * 3;

    // Set world bounds for physics (larger than screen)
    this.matter.world.setBounds(
      0,
      0,
      this.HUB_WORLD_WIDTH,
      this.HUB_WORLD_HEIGHT
    );

    // Create player at the center of the Hub world
    const playerStartX = this.HUB_WORLD_WIDTH / 2;
    const playerStartY = this.HUB_WORLD_HEIGHT / 2;
    this.player = new Player(this, playerStartX, playerStartY);

    // Set player to Free movement mode for the Hub
    this.player.setMovementMode(MovementMode.Free);

    // Configure camera to follow the player
    this.setupCamera();

    // Create teleport zone
    this.createTeleport();

    // Set up collision detection
    this.setupCollisions();

    // Add some visual boundaries (optional - just for reference)
    this.createHubBoundaries();
  }

  private setupCamera(): void {
    // Set camera bounds to the Hub world size
    this.cameras.main.setBounds(
      0,
      0,
      this.HUB_WORLD_WIDTH,
      this.HUB_WORLD_HEIGHT
    );

    // Make camera follow the player
    this.cameras.main.startFollow(this.player, true, 0.1, 0.1);

    // Set camera deadzone to keep player centered
    this.cameras.main.setDeadzone(0, 0);
  }

  private createHubBoundaries(): void {
    // Create visual boundaries to show the Hub area edges
    const graphics = this.add.graphics();
    graphics.lineStyle(2, 0x444444, 1);

    // Draw rectangle outline for Hub boundaries
    graphics.strokeRect(0, 0, this.HUB_WORLD_WIDTH, this.HUB_WORLD_HEIGHT);

    // Add some grid lines for reference (every 160 pixels - half virtual width)
    graphics.lineStyle(1, 0x555555, 0.5);

    // Vertical grid lines
    for (let x = 160; x < this.HUB_WORLD_WIDTH; x += 160) {
      graphics.moveTo(x, 0);
      graphics.lineTo(x, this.HUB_WORLD_HEIGHT);
    }

    // Horizontal grid lines
    for (let y = 120; y < this.HUB_WORLD_HEIGHT; y += 120) {
      graphics.moveTo(0, y);
      graphics.lineTo(this.HUB_WORLD_WIDTH, y);
    }

    graphics.strokePath();
  }

  private createTeleport(): void {
    // Position teleport in the upper-left area of the Hub world
    const teleportX = this.HUB_WORLD_WIDTH * 0.1; // 20% from left edge
    const teleportY = this.HUB_WORLD_HEIGHT * 0.1; // 20% from top edge

    this.teleport = new Teleport(this, teleportX, teleportY);
  }

  private setupCollisions(): void {
    // Set up Matter.js collision detection
    this.matter.world.on("collisionstart", (event: any) => {
      const pairs = event.pairs;

      for (let i = 0; i < pairs.length; i++) {
        const bodyA = pairs[i].bodyA;
        const bodyB = pairs[i].bodyB;
        const gameObjectA = bodyA.gameObject;
        const gameObjectB = bodyB.gameObject;

        if (!gameObjectA || !gameObjectB) continue;

        // Check for player-teleport collision
        if (this.isPlayerTeleportCollision(gameObjectA, gameObjectB)) {
          this.teleport.onPlayerEnter();
        }
      }
    });

    this.matter.world.on("collisionend", (event: any) => {
      const pairs = event.pairs;

      for (let i = 0; i < pairs.length; i++) {
        const bodyA = pairs[i].bodyA;
        const bodyB = pairs[i].bodyB;
        const gameObjectA = bodyA.gameObject;
        const gameObjectB = bodyB.gameObject;

        if (!gameObjectA || !gameObjectB) continue;

        // Check for player-teleport collision end
        if (this.isPlayerTeleportCollision(gameObjectA, gameObjectB)) {
          this.teleport.onPlayerExit();
        }
      }
    });
  }

  private isPlayerTeleportCollision(objA: any, objB: any): boolean {
    return (
      (objA === this.player && objB === this.teleport) ||
      (objA === this.teleport && objB === this.player)
    );
  }

  update(time: number, delta: number): void {
    // Update input system
    this.inputSystem.update();

    // Update player
    this.player.update(time, delta);

    // Update teleport
    this.teleport.update(time, delta);

    // Handle teleport activation
    if (this.teleport.isPlayerInZone() && this.inputSystem.pressed("action1")) {
      this.teleport.activate();
      this.scene.start("Level");
    }

    // Handle debug key to switch to Level scene (for testing)
    if (this.inputSystem.pressed("action2")) {
      this.scene.start("Level");
    }
  }
}

export { Hub };
