import { Scene } from "phaser";
import { InputSystem } from "@/systems/InputSystem";
import { GameConfig } from "../config/GameConfig";
import { ResolutionManager } from "../systems/ResolutionManager";

export class GameOver extends Scene {
  camera: Phaser.Cameras.Scene2D.Camera;
  background: Phaser.GameObjects.Image;
  gameover_text: Phaser.GameObjects.Text;
  inputSystem: InputSystem;

  // Resolution management
  private gameConfig: GameConfig;
  private resolutionManager: ResolutionManager;

  constructor() {
    super("GameOver");

    // Initialize resolution management
    this.gameConfig = GameConfig.getInstance();
    this.resolutionManager = ResolutionManager.getInstance();
  }

  create() {
    this.camera = this.cameras.main;
    this.camera.setBackgroundColor(0x000000);

    // Get virtual resolution for positioning
    const virtualRes = this.resolutionManager.getVirtualResolution();
    const scaleFactor = this.resolutionManager.getScaleFactor();

    // Position using screen coordinates
    const targetRes = this.resolutionManager.getTargetResolution();
    this.gameover_text = this.add.text(
      targetRes.width / 2,
      targetRes.height / 2,
      "Game Over",
      {
        fontFamily: "Arial Black",
        fontSize: 16 * scaleFactor, // Scale font size
        color: "#ffffff",
        stroke: "#000000",
        strokeThickness: 2 * scaleFactor, // Scale stroke thickness
        align: "center",
      }
    );
    this.gameover_text.setOrigin(0.5);

    this.inputSystem = InputSystem.getInstance();
    this.inputSystem.initialize(this);
  }

  update(): void {
    if (this.inputSystem.released("action1")) {
      console.log("Restarting level");
      this.scene.start("Level");
    }
  }
}
