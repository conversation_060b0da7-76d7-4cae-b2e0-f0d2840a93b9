import Phaser from "phaser";
import { InputSystem } from "@/systems/InputSystem";
import { ResolutionManager } from "@/systems/ResolutionManager";
import { LevelDataManager } from "@/systems/LevelDataManager";
import { EntityTile } from "@/types/LevelData";

class LevelEditor extends Phaser.Scene {
  // Constants
  private readonly VIRTUAL_TILE_SIZE = 16; // 16x16 virtual pixel tiles
  private readonly GRID_COLOR = 0x333333; // Dark gray grid lines
  private readonly SCROLL_SPEED = 200; // Pixels per second

  // Resolution management
  private resolutionManager: ResolutionManager;

  // Input system
  private inputSystem: InputSystem;

  // Level data management
  private levelDataManager: LevelDataManager;
  private readonly SESSION_STORAGE_KEY = "genesis-level-editor-data";

  // Grid rendering
  private gridGraphics: Phaser.GameObjects.Graphics;
  private lastCameraX: number = 0;
  private lastCameraY: number = 0;
  private readonly GRID_RENDER_THRESHOLD = 8; // Re-render grid when camera moves this many pixels

  // UI elements
  private coordinateText: Phaser.GameObjects.Text;
  private tileHighlight: Phaser.GameObjects.Graphics;
  private uiBackground: Phaser.GameObjects.Graphics;
  private runLevelButton: {
    bg: Phaser.GameObjects.Graphics;
    text: Phaser.GameObjects.Text;
  } | null = null;
  private clearLevelButton: {
    bg: Phaser.GameObjects.Graphics;
    text: Phaser.GameObjects.Text;
  } | null = null;
  private readonly UI_HEIGHT_TILES = 2; // UI area height in tiles

  // Entity rendering
  private entitySprites: Map<string, Phaser.GameObjects.Sprite> = new Map();
  private isPlacingEntities: boolean = false;
  private currentEnemyTier: number = 1; // Current enemy tier to place (1-3)

  // Camera bounds and world
  private worldBounds: { minY: number; maxY: number };

  constructor() {
    super("LevelEditor");

    // Initialize resolution management
    this.resolutionManager = ResolutionManager.getInstance();

    // Initialize level data manager
    this.levelDataManager = new LevelDataManager();

    // Load level data from session storage if available
    this.loadLevelDataFromSession();

    // Initialize world bounds
    this.worldBounds = {
      minY: 0, // Y=0 is the bottom row (where players start)
      maxY: 1000, // Allow scrolling up to Y=1000 for now (can be expanded)
    };
  }

  private getTileSize(): number {
    // Return the scaled tile size based on the current resolution
    return this.resolutionManager.scaleValue(this.VIRTUAL_TILE_SIZE);
  }

  private getUIHeight(): number {
    // Return the UI area height in pixels
    return this.getTileSize() * this.UI_HEIGHT_TILES;
  }

  // Level data helper methods
  public getLevelDataManager(): LevelDataManager {
    return this.levelDataManager;
  }

  public addEntity(
    x: number,
    y: number,
    type: string = "Enemy",
    tier: number = 1
  ): void {
    // Check if entity already exists at this position
    const key = `${x},${y}`;
    if (this.entitySprites.has(key)) {
      return; // Don't add duplicate
    }

    this.levelDataManager.setEntityTile(x, y, type, tier);
    this.createEntitySprite(x, y, type);
    this.saveLevelDataToSession(); // Save to session storage
    console.log(`Added ${type} (tier ${tier}) at tile (${x}, ${y})`);
  }

  public removeEntity(x: number, y: number): boolean {
    const removed = this.levelDataManager.removeTile(x, y, "entities");
    if (removed) {
      this.destroyEntitySprite(x, y);
      this.saveLevelDataToSession(); // Save to session storage
      console.log(`Removed entity at tile (${x}, ${y})`);
    }
    return removed;
  }

  public getEntityAt(x: number, y: number) {
    return this.levelDataManager.getTile(x, y, "entities");
  }

  public hasEntityAt(x: number, y: number): boolean {
    return this.levelDataManager.getTile(x, y, "entities") !== null;
  }

  // Session storage methods for persistence
  private saveLevelDataToSession(): void {
    try {
      const levelData = this.levelDataManager.getLevelData();
      sessionStorage.setItem(
        this.SESSION_STORAGE_KEY,
        JSON.stringify(levelData)
      );
      console.log("Level data saved to session storage");
    } catch (error) {
      console.warn("Failed to save level data to session storage:", error);
    }
  }

  private loadLevelDataFromSession(): boolean {
    try {
      const savedData = sessionStorage.getItem(this.SESSION_STORAGE_KEY);
      if (savedData) {
        const levelData = JSON.parse(savedData);
        // Convert date strings back to Date objects
        if (levelData.metadata) {
          if (levelData.metadata.created) {
            levelData.metadata.created = new Date(levelData.metadata.created);
          }
          if (levelData.metadata.modified) {
            levelData.metadata.modified = new Date(levelData.metadata.modified);
          }
        }
        this.levelDataManager.loadLevelData(levelData);
        console.log("Level data loaded from session storage");
        return true;
      }
    } catch (error) {
      console.warn("Failed to load level data from session storage:", error);
    }
    return false;
  }

  private clearSessionStorage(): void {
    try {
      sessionStorage.removeItem(this.SESSION_STORAGE_KEY);
      console.log("Level data cleared from session storage");
    } catch (error) {
      console.warn("Failed to clear level data from session storage:", error);
    }
  }

  public clearLevel(): void {
    // Clear all level data
    this.levelDataManager.clearLayer("entities");
    this.levelDataManager.clearLayer("structures");
    this.levelDataManager.clearLayer("background");

    // Clear all entity sprites
    this.entitySprites.forEach((sprite) => sprite.destroy());
    this.entitySprites.clear();

    // Clear session storage
    this.clearSessionStorage();

    console.log("Level cleared");
  }

  public getLevelStats() {
    return this.levelDataManager.getStats();
  }

  create() {
    console.log("LevelEditor: Scene created");
    console.log("Level stats:", this.getLevelStats());

    // Stop Debug scene if it's running (cleanup from Level scene)
    if (this.scene.isActive("Debug")) {
      this.scene.stop("Debug");
    }

    // Set up camera bounds for the target resolution
    const targetRes = this.resolutionManager.getTargetResolution();

    // Set camera bounds to allow infinite upward scrolling
    // We'll enforce the Y=0 minimum boundary in the update loop
    const tileSize = this.getTileSize();
    this.cameras.main.setBounds(
      0,
      -this.worldBounds.maxY * tileSize, // Negative because Phaser Y increases downward
      targetRes.width,
      (this.worldBounds.maxY + targetRes.height / tileSize) * tileSize
    );

    // Position camera to show Y=0 tiles at the bottom of the screen
    // In Phaser, Y increases downward, so Y=0 in our coordinate system
    // should be at the bottom of the screen
    const cameraStartY = -targetRes.height + tileSize;
    this.cameras.main.setScroll(0, cameraStartY);

    // Initialize input system
    this.inputSystem = InputSystem.getInstance();
    this.inputSystem.initialize(this);

    // Set up mouse wheel scrolling
    this.input.on("wheel", this.handleMouseWheel, this);

    // Prevent browser context menu on right-click
    this.input.mouse?.disableContextMenu();

    // Create grid graphics
    this.gridGraphics = this.add.graphics();
    this.gridGraphics.setDepth(-1); // Render behind other objects

    // Create tile highlight graphics
    this.tileHighlight = this.add.graphics();
    this.tileHighlight.setDepth(1); // Render above grid but below UI

    // Entity sprites will be created dynamically as needed

    // Store initial camera position
    this.lastCameraX = this.cameras.main.scrollX;
    this.lastCameraY = this.cameras.main.scrollY;

    // Initial grid render
    this.renderGrid();

    // Initial entity render
    this.renderAllEntities();

    // Create UI area
    this.createUIArea();

    // Add coordinate display
    this.createCoordinateDisplay();

    // Create Run Level button
    this.createRunLevelButton();

    // Create Clear Level button
    this.createClearLevelButton();

    console.log("Level editor ready. Stats:", this.getLevelStats());
  }

  private createEntitySprite(x: number, y: number, type: string): void {
    if (type !== "Enemy") return; // Only handle enemies for now

    const tileSize = this.getTileSize();
    const key = `${x},${y}`;

    // Convert our coordinate system to Phaser world coordinates
    const worldX = x * tileSize;
    const worldY = -y * tileSize; // Flip Y back to Phaser coordinates

    // Calculate center position
    const centerX = worldX + tileSize / 2;
    const centerY = worldY + tileSize / 2;

    // Get the entity data to determine which sprite to use
    const entityData = this.levelDataManager.getTile(x, y, "entities");
    let spriteTexture = "enemy1"; // Default to enemy1

    if (entityData && "tier" in entityData) {
      // Use different sprites based on tier
      switch (entityData.tier) {
        case 1:
          spriteTexture = "enemy1";
          break;
        case 2:
          spriteTexture = "enemy2";
          break;
        case 3:
          spriteTexture = "enemy3";
          break;
        default:
          spriteTexture = "enemy1";
          break;
      }
    }

    // Create enemy sprite using the appropriate texture
    const sprite = this.add.sprite(centerX, centerY, spriteTexture);

    // Scale the sprite similar to how the Enemy class does it
    const scaleFactor = this.resolutionManager.getScaleFactor();
    sprite.setScale(0.07 * scaleFactor); // Same scaling as Enemy class

    // Set depth to render above highlights but below UI
    sprite.setDepth(2);

    // Store sprite with coordinate key for easy lookup
    this.entitySprites.set(key, sprite);
  }

  private destroyEntitySprite(x: number, y: number): void {
    const key = `${x},${y}`;
    const sprite = this.entitySprites.get(key);
    if (sprite) {
      sprite.destroy();
      this.entitySprites.delete(key);
    }
  }

  private renderAllEntities(): void {
    console.log("Rendering all entities (initial load)");

    const entities = this.levelDataManager.getLayerTiles(
      "entities"
    ) as EntityTile[];

    // Clear existing sprites (only needed for initial load or full refresh)
    this.entitySprites.forEach((sprite) => sprite.destroy());
    this.entitySprites.clear();

    // Create sprites for each entity using the helper method
    for (const entity of entities) {
      this.createEntitySprite(entity.x, entity.y, entity.type);
    }
  }

  private updateUIText(): void {
    this.coordinateText.setText(
      `Left click: Place Enemy Tier ${this.currentEnemyTier} | Right click: Remove | [1-3]: Change tier | Drag to place multiple`
    );
  }

  shutdown(): void {
    // Clean up entity sprites
    this.entitySprites.forEach((sprite) => sprite.destroy());
    this.entitySprites.clear();
  }

  private createUIArea(): void {
    const targetRes = this.resolutionManager.getTargetResolution();
    const uiHeight = this.getUIHeight();

    // Create UI background
    this.uiBackground = this.add.graphics();
    this.uiBackground.fillStyle(0x666666, 1.0); // Dark grey with 90% opacity
    this.uiBackground.fillRect(0, 0, targetRes.width, uiHeight);
    this.uiBackground.setScrollFactor(0); // Keep UI fixed on screen
    this.uiBackground.setDepth(50); // Above grid and highlights, below text
  }

  private createCoordinateDisplay(): void {
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const uiHeight = this.getUIHeight();

    this.coordinateText = this.add.text(
      15 * scaleFactor,
      uiHeight / 2,
      `Left click: Place Enemy Tier ${this.currentEnemyTier} | Right click: Remove | [1-3]: Change tier | Drag to place multiple`,
      {
        fontSize: `${10 * scaleFactor}px`,
        color: "#ffffff",
        fontFamily: "Arial",
      }
    );
    this.coordinateText.setScrollFactor(0); // Keep text fixed on screen
    this.coordinateText.setDepth(100); // Ensure it's on top of UI
    this.coordinateText.setOrigin(0, 0.5); // Center vertically
  }

  private createRunLevelButton(): void {
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const targetRes = this.resolutionManager.getTargetResolution();
    const uiHeight = this.getUIHeight();

    // Button dimensions
    const buttonWidth = 60 * scaleFactor;
    const buttonHeight = 16 * scaleFactor;
    const buttonX = targetRes.width - buttonWidth / 2 - 10 * scaleFactor;
    const buttonY = uiHeight / 2;

    // Create button background
    const bg = this.add.graphics();
    bg.fillStyle(0x4a5568); // Dark gray background
    bg.fillRoundedRect(
      buttonX - buttonWidth / 2,
      buttonY - buttonHeight / 2,
      buttonWidth,
      buttonHeight,
      6
    );

    // Border
    bg.lineStyle(2, 0x81a3c1); // Light blue border
    bg.strokeRoundedRect(
      buttonX - buttonWidth / 2,
      buttonY - buttonHeight / 2,
      buttonWidth,
      buttonHeight,
      6
    );

    // Inner glow effect
    bg.fillStyle(0x6b7280, 0.3);
    bg.fillRoundedRect(
      buttonX - buttonWidth / 2 + 2,
      buttonY - buttonHeight / 2 + 2,
      buttonWidth - 4,
      buttonHeight - 4,
      4
    );

    // Button text
    const buttonText = this.add
      .text(buttonX, buttonY, "Run Level", {
        fontSize: `${8 * scaleFactor}px`,
        color: "#e2e8f0",
        fontFamily: "Arial",
      })
      .setOrigin(0.5);

    // Keep UI elements fixed on screen
    bg.setScrollFactor(0);
    buttonText.setScrollFactor(0);
    bg.setDepth(100);
    buttonText.setDepth(101);

    // Make interactive
    const hitArea = new Phaser.Geom.Rectangle(
      buttonX - buttonWidth / 2,
      buttonY - buttonHeight / 2,
      buttonWidth,
      buttonHeight
    );
    bg.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);

    // Hover effects
    bg.on(Phaser.Input.Events.POINTER_OVER, () => {
      bg.clear();
      bg.fillStyle(0x5a6578); // Lighter gray on hover
      bg.fillRoundedRect(
        buttonX - buttonWidth / 2,
        buttonY - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );
      bg.lineStyle(2, 0x93b5d1); // Brighter blue border
      bg.strokeRoundedRect(
        buttonX - buttonWidth / 2,
        buttonY - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );
      bg.fillStyle(0x7b8794, 0.4);
      bg.fillRoundedRect(
        buttonX - buttonWidth / 2 + 2,
        buttonY - buttonHeight / 2 + 2,
        buttonWidth - 4,
        buttonHeight - 4,
        4
      );
      buttonText.setStyle({ color: "#f1f5f9" });
    });

    bg.on(Phaser.Input.Events.POINTER_OUT, () => {
      bg.clear();
      bg.fillStyle(0x4a5568);
      bg.fillRoundedRect(
        buttonX - buttonWidth / 2,
        buttonY - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );
      bg.lineStyle(2, 0x81a3c1);
      bg.strokeRoundedRect(
        buttonX - buttonWidth / 2,
        buttonY - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );
      bg.fillStyle(0x6b7280, 0.3);
      bg.fillRoundedRect(
        buttonX - buttonWidth / 2 + 2,
        buttonY - buttonHeight / 2 + 2,
        buttonWidth - 4,
        buttonHeight - 4,
        4
      );
      buttonText.setStyle({ color: "#e2e8f0" });
    });

    // Click handler
    bg.on(Phaser.Input.Events.POINTER_DOWN, () => {
      console.log("Running level with current data");
      const levelData = this.levelDataManager.getLevelData();
      this.scene.start("Level", { levelData });
    });

    // Store button reference
    this.runLevelButton = { bg, text: buttonText };
  }

  private createClearLevelButton(): void {
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const targetRes = this.resolutionManager.getTargetResolution();
    const uiHeight = this.getUIHeight();

    // Button dimensions
    const buttonWidth = 60 * scaleFactor;
    const buttonHeight = 16 * scaleFactor;
    const spacing = 5 * scaleFactor;
    // Position to the left of the Run Level button
    const buttonX =
      targetRes.width - buttonWidth * 1.5 - spacing - 10 * scaleFactor;
    const buttonY = uiHeight / 2;

    // Create button background
    const bg = this.add.graphics();
    bg.fillStyle(0x5a4a4a); // Darker reddish background
    bg.fillRoundedRect(
      buttonX - buttonWidth / 2,
      buttonY - buttonHeight / 2,
      buttonWidth,
      buttonHeight,
      6
    );

    // Border
    bg.lineStyle(2, 0xc18181); // Light red border
    bg.strokeRoundedRect(
      buttonX - buttonWidth / 2,
      buttonY - buttonHeight / 2,
      buttonWidth,
      buttonHeight,
      6
    );

    // Inner glow effect
    bg.fillStyle(0x806b6b, 0.3);
    bg.fillRoundedRect(
      buttonX - buttonWidth / 2 + 2,
      buttonY - buttonHeight / 2 + 2,
      buttonWidth - 4,
      buttonHeight - 4,
      4
    );

    // Button text
    const buttonText = this.add
      .text(buttonX, buttonY, "Clear Level", {
        fontSize: `${8 * scaleFactor}px`,
        color: "#f0e2e2",
        fontFamily: "Arial",
      })
      .setOrigin(0.5);

    // Set UI properties
    bg.setScrollFactor(0);
    buttonText.setScrollFactor(0);
    bg.setDepth(100);
    buttonText.setDepth(101);

    // Make button interactive
    const hitArea = new Phaser.Geom.Rectangle(
      buttonX - buttonWidth / 2,
      buttonY - buttonHeight / 2,
      buttonWidth,
      buttonHeight
    );
    bg.setInteractive(hitArea, Phaser.Geom.Rectangle.Contains);

    // Hover effects
    bg.on(Phaser.Input.Events.POINTER_OVER, () => {
      bg.clear();
      bg.fillStyle(0x6a5a5a); // Lighter on hover
      bg.fillRoundedRect(
        buttonX - buttonWidth / 2,
        buttonY - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );
      bg.lineStyle(2, 0xd19393); // Brighter red border
      bg.strokeRoundedRect(
        buttonX - buttonWidth / 2,
        buttonY - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );
      bg.fillStyle(0x947b7b, 0.4);
      bg.fillRoundedRect(
        buttonX - buttonWidth / 2 + 2,
        buttonY - buttonHeight / 2 + 2,
        buttonWidth - 4,
        buttonHeight - 4,
        4
      );
      buttonText.setStyle({ color: "#f9f1f1" });
    });

    bg.on(Phaser.Input.Events.POINTER_OUT, () => {
      bg.clear();
      bg.fillStyle(0x5a4a4a);
      bg.fillRoundedRect(
        buttonX - buttonWidth / 2,
        buttonY - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );
      bg.lineStyle(2, 0xc18181);
      bg.strokeRoundedRect(
        buttonX - buttonWidth / 2,
        buttonY - buttonHeight / 2,
        buttonWidth,
        buttonHeight,
        6
      );
      bg.fillStyle(0x806b6b, 0.3);
      bg.fillRoundedRect(
        buttonX - buttonWidth / 2 + 2,
        buttonY - buttonHeight / 2 + 2,
        buttonWidth - 4,
        buttonHeight - 4,
        4
      );
      buttonText.setStyle({ color: "#f0e2e2" });
    });

    // Click handler
    bg.on(Phaser.Input.Events.POINTER_DOWN, () => {
      console.log("Clearing level data");
      this.clearLevel();
    });

    // Store button reference
    this.clearLevelButton = { bg, text: buttonText };
  }

  private renderGrid(): void {
    this.gridGraphics.clear();

    const targetRes = this.resolutionManager.getTargetResolution();
    const tileSize = this.getTileSize();

    // Calculate visible area in world coordinates
    const camera = this.cameras.main;
    const visibleLeft = camera.scrollX;
    const visibleRight = camera.scrollX + targetRes.width;
    const visibleTop = camera.scrollY;
    const visibleBottom = camera.scrollY + targetRes.height;

    // Calculate tile bounds to render (with some padding for smooth scrolling)
    const padding = tileSize * 2;
    const startTileX = Math.floor((visibleLeft - padding) / tileSize);
    const endTileX = Math.ceil((visibleRight + padding) / tileSize);
    const startTileY = Math.floor((visibleTop - padding) / tileSize);
    const endTileY = Math.ceil((visibleBottom + padding) / tileSize);

    // Set line style - always use 1 pixel width regardless of scale factor
    this.gridGraphics.lineStyle(1, this.GRID_COLOR, 0.8);

    // Draw vertical grid lines
    for (let tileX = startTileX; tileX <= endTileX; tileX++) {
      const worldX = tileX * tileSize;
      this.gridGraphics.lineBetween(
        worldX,
        visibleTop - padding,
        worldX,
        visibleBottom + padding
      );
    }

    // Draw horizontal grid lines
    for (let tileY = startTileY; tileY <= endTileY; tileY++) {
      const worldY = tileY * tileSize;
      this.gridGraphics.lineBetween(
        visibleLeft - padding,
        worldY,
        visibleRight + padding,
        worldY
      );
    }
  }

  update(_time: number, delta: number): void {
    // Update input system
    this.inputSystem.update();

    // Handle scene navigation
    if (
      this.inputSystem.pressed("select") ||
      this.input.keyboard?.checkDown(this.input.keyboard.addKey("ESC"))
    ) {
      this.scene.start("Level");
      return;
    }

    // Handle enemy tier selection
    if (
      this.input.keyboard?.checkDown(this.input.keyboard.addKey("ONE"), 100)
    ) {
      this.currentEnemyTier = 1;
      this.updateUIText();
    } else if (
      this.input.keyboard?.checkDown(this.input.keyboard.addKey("TWO"), 100)
    ) {
      this.currentEnemyTier = 2;
      this.updateUIText();
    } else if (
      this.input.keyboard?.checkDown(this.input.keyboard.addKey("THREE"), 100)
    ) {
      this.currentEnemyTier = 3;
      this.updateUIText();
    }

    // Handle camera scrolling
    this.handleCameraMovement(delta);

    // Handle entity placement
    this.handleEntityPlacement();

    // Update coordinate display based on mouse position
    this.updateCoordinateDisplay();

    // Re-render grid if camera moved significantly
    this.updateGridIfNeeded();

    // Note: Individual entity rendering is now handled directly in add/remove methods
  }

  private handleMouseWheel(
    _pointer: Phaser.Input.Pointer,
    _gameObjects: Phaser.GameObjects.GameObject[],
    _deltaX: number,
    deltaY: number,
    _deltaZ: number
  ): void {
    const camera = this.cameras.main;
    const wheelSensitivity = 2; // Pixels per wheel step

    // Vertical scrolling (deltaY)
    let newScrollY = camera.scrollY + deltaY * wheelSensitivity;

    // Enforce Y=0 minimum boundary
    const targetRes = this.resolutionManager.getTargetResolution();
    const tileSize = this.getTileSize();
    const maxScrollY = -targetRes.height + tileSize;

    if (newScrollY > maxScrollY) {
      newScrollY = maxScrollY;
    }

    // Apply camera movement
    camera.setScroll(camera.scrollX, newScrollY);
  }

  private handleEntityPlacement(): void {
    const pointer = this.input.activePointer;
    if (!pointer) return;

    // Check if mouse is over UI area
    const uiHeight = this.getUIHeight();
    if (pointer.y < uiHeight) return;

    // Get tile coordinates
    const camera = this.cameras.main;
    const worldX = pointer.x + camera.scrollX;
    const worldY = pointer.y + camera.scrollY;
    const tileSize = this.getTileSize();
    const tileX = Math.floor(worldX / tileSize);
    const tileY = -Math.floor(worldY / tileSize);

    // Handle mouse input for entity placement
    if (pointer.leftButtonDown()) {
      if (!this.isPlacingEntities) {
        this.isPlacingEntities = true;
      }

      // Place entity if not already there
      if (!this.hasEntityAt(tileX, tileY)) {
        this.addEntity(tileX, tileY, "Enemy", this.currentEnemyTier);
      }
    } else if (pointer.rightButtonDown()) {
      // Remove entity with right click
      if (this.hasEntityAt(tileX, tileY)) {
        this.removeEntity(tileX, tileY);
      }
    } else {
      this.isPlacingEntities = false;
    }
  }

  private updateCoordinateDisplay(): void {
    // Get mouse position in screen coordinates
    const pointer = this.input.activePointer;
    if (!pointer) {
      // Clear highlight when no pointer
      this.tileHighlight.clear();
      this.coordinateText.setText(
        `Left click: Place Enemy Tier ${this.currentEnemyTier} | Right click: Remove | [1-3]: Change tier | Drag to place multiple`
      );
      return;
    }

    // Check if mouse is over UI area
    const uiHeight = this.getUIHeight();
    if (pointer.y < uiHeight) {
      // Mouse is over UI area, clear highlight and show default text
      this.tileHighlight.clear();
      this.coordinateText.setText(
        `Left click: Place Enemy Tier ${this.currentEnemyTier} | Right click: Remove | [1-3]: Change tier | Drag to place multiple`
      );
      return;
    }

    // Convert screen coordinates to world coordinates
    const camera = this.cameras.main;
    const worldX = pointer.x + camera.scrollX;
    const worldY = pointer.y + camera.scrollY;

    // Convert world coordinates to tile coordinates
    const tileSize = this.getTileSize();
    const tileX = Math.floor(worldX / tileSize);

    // Convert Y coordinate to our coordinate system (Y=0 at bottom, increasing upward)
    // In Phaser, Y=0 is at top, so we need to flip it
    const tileY = -Math.floor(worldY / tileSize);

    // Check if there's an entity at this position
    const entity = this.getEntityAt(tileX, tileY) as EntityTile | null;
    const entityInfo = entity ? ` | ${entity.type} (T${entity.tier})` : "";

    // Update the coordinate text
    this.coordinateText.setText(`Tile: X=${tileX}, Y=${tileY}${entityInfo}`);

    // Update tile highlight
    this.updateTileHighlight(tileX, tileY);
  }

  private updateTileHighlight(tileX: number, tileY: number): void {
    this.tileHighlight.clear();

    const tileSize = this.getTileSize();

    // Convert our coordinate system back to Phaser world coordinates
    const worldX = tileX * tileSize;
    const worldY = -tileY * tileSize; // Flip Y back to Phaser coordinates

    // Draw semi-transparent white rectangle
    this.tileHighlight.fillStyle(0xffffff, 0.3); // White with 30% opacity
    this.tileHighlight.fillRect(worldX, worldY, tileSize, tileSize);
  }

  private updateGridIfNeeded(): void {
    const camera = this.cameras.main;
    const deltaX = Math.abs(camera.scrollX - this.lastCameraX);
    const deltaY = Math.abs(camera.scrollY - this.lastCameraY);

    // Only re-render if camera moved significantly
    if (
      deltaX > this.GRID_RENDER_THRESHOLD ||
      deltaY > this.GRID_RENDER_THRESHOLD
    ) {
      this.renderGrid();
      this.lastCameraX = camera.scrollX;
      this.lastCameraY = camera.scrollY;
    }
  }

  private handleCameraMovement(delta: number): void {
    const camera = this.cameras.main;
    const movement = this.inputSystem.getMovementVector();
    const scrollSpeed = this.SCROLL_SPEED * (delta / 1000); // Convert to pixels per frame

    if (movement.x !== 0 || movement.y !== 0) {
      // Calculate new camera position
      let newScrollX = camera.scrollX + movement.x * scrollSpeed;
      let newScrollY = camera.scrollY + movement.y * scrollSpeed;

      // Enforce Y=0 minimum boundary
      // In our coordinate system, Y=0 should be at the bottom of the screen
      // In Phaser coordinates, this means the camera's bottom edge should not go below Y=0
      const targetRes = this.resolutionManager.getTargetResolution();
      const tileSize = this.getTileSize();
      const maxScrollY = -targetRes.height + tileSize; // Camera bottom at Y=0

      if (newScrollY > maxScrollY) {
        newScrollY = maxScrollY;
      }

      // Apply camera movement
      camera.setScroll(newScrollX, newScrollY);
    }
  }
}

export { LevelEditor };
