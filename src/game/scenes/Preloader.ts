import { Scene } from "phaser";

export class Preloader extends Scene {
  constructor() {
    super("Preloader");
  }

  init() {
    //  A simple progress bar. This is the outline of the bar.
    this.add.rectangle(512, 384, 468, 32).setStrokeStyle(1, 0xffffff);

    //  This is the progress bar itself. It will increase in size from the left based on the % of progress.
    const bar = this.add.rectangle(512 - 230, 384, 4, 28, 0xffffff);

    //  Use the 'progress' event emitted by the LoaderPlugin to update the loading bar
    this.load.on("progress", (progress: number) => {
      //  Update the progress bar (our bar is 464px wide, so 100% = 464px)
      bar.width = 4 + 460 * progress;
    });
  }

  preload() {
    //  Load the assets for the game - Replace with your own assets
    this.load.setPath("assets");

    // Load ship sprites
    this.load.image("player", "player.png");
    this.load.image("enemy1", "enemy1.png");
    this.load.image("enemy2", "enemy2.png");
    this.load.image("enemy3", "enemy3.png");
    this.load.image("bullet", "bullet.png");
  }

  create() {
    //  When all the assets have loaded, it's often worth creating global objects here that the rest of the game can use.
    //  For example, you can define global animations here, so we can use them in other scenes.
    //  Move to the MainMenu. You could also swap this for a Scene Transition, such as a camera fade.
    const lastScene = sessionStorage.getItem("lastScene");
    if (lastScene) {
      // If restoring the Level scene, check for level editor data
      if (lastScene === "Level") {
        const levelEditorData = sessionStorage.getItem(
          "genesis-level-editor-data"
        );
        if (levelEditorData) {
          try {
            const levelData = JSON.parse(levelEditorData);
            // Convert date strings back to Date objects
            if (levelData.metadata) {
              if (levelData.metadata.created) {
                levelData.metadata.created = new Date(
                  levelData.metadata.created
                );
              }
              if (levelData.metadata.modified) {
                levelData.metadata.modified = new Date(
                  levelData.metadata.modified
                );
              }
            }
            this.scene.start(lastScene, { levelData });
          } catch (error) {
            console.warn(
              "Failed to parse level editor data during HMR restoration:",
              error
            );
            this.scene.start(lastScene);
          }
        } else {
          this.scene.start(lastScene);
        }
      } else {
        this.scene.start(lastScene);
      }
      sessionStorage.removeItem("lastScene");
    } else {
      this.scene.start("Hub");
    }
  }
}
