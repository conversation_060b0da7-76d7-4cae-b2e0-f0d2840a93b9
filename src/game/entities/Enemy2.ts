import { Enemy, EnemyConfig } from "./Enemy";
import { BulletManager } from "../systems/BulletManager";
import { ResolutionManager } from "../systems/ResolutionManager";
import { GameConfig } from "../config/GameConfig";

/**
 * Enemy Type 2: Horizontal Mover
 * - Moves horizontally while also moving downward due to level scrolling
 * - Initial direction depends on spawn position relative to screen center
 * - Reverses direction when approaching screen boundaries
 * - Same speed as Enemy1 but with horizontal movement
 */
class Enemy2 extends Enemy {
  private horizontalDirection: number = 1; // 1 for right, -1 for left
  private worldWidth: number;
  private BOUNDARY_MARGIN: number = 30; // Pixels from edge to reverse

  constructor(
    scene: Phaser.Scene,
    x?: number,
    y?: number,
    config: EnemyConfig = {},
    bulletManager: BulletManager | null = null
  ) {
    // Set default configuration for Enemy2
    const scaleFactor = ResolutionManager.getInstance().getScaleFactor();
    const globalDownwardSpeed =
      GameConfig.getInstance().getGlobalDownwardSpeed();
    const enemy2Config: EnemyConfig = {
      maxHealth: 1,
      color: 0x44ff44, // Light green
      size: 0.8,
      speed: globalDownwardSpeed * scaleFactor, // Use global downward speed as base
      turnSpeed: 0, // No gradual turning needed
      fireRate: 2500, // Medium shooting rate
      shootInFacingDirection: true,
      scoreValue: 75, // Medium score value
      ...config, // Allow overrides
    };

    // Call parent constructor with enemy2 texture
    super(scene, x, y, enemy2Config, bulletManager, "enemy2");

    // Get world width for boundary detection
    const resolutionManager = ResolutionManager.getInstance();
    const targetRes = resolutionManager.getTargetResolution();
    this.worldWidth = targetRes.width;
    this.BOUNDARY_MARGIN = this.BOUNDARY_MARGIN * scaleFactor;

    // Determine initial horizontal direction based on spawn position
    const centerX = this.worldWidth / 2;
    if (x !== undefined) {
      // If spawned on left half, move right; if on right half, move left
      this.horizontalDirection = x < centerX ? 1 : -1;
    }

    // Re-initialize movement with the correct direction
    this.initializeMovement();
  }

  protected initializeMovement(): void {
    // Get global downward speed for consistent vertical movement
    const scaleFactor = ResolutionManager.getInstance().getScaleFactor();
    const globalDownwardSpeed =
      GameConfig.getInstance().getGlobalDownwardSpeed() * scaleFactor;

    // Use the configured speed for horizontal movement
    const baseSpeed = this.speed || globalDownwardSpeed;

    // Move horizontally and downward
    const horizontalSpeed = baseSpeed * this.horizontalDirection;
    const verticalSpeed = globalDownwardSpeed; // Same downward speed as Enemy1

    this.setVelocity(horizontalSpeed, verticalSpeed);

    // Face in the direction of horizontal movement
    this.rotation = this.horizontalDirection > 0 ? Math.PI / 2 : -Math.PI / 2;
  }

  protected updateMovement(
    time: number,
    delta: number,
    playerPosition?: { x: number; y: number }
  ): void {
    // Check for boundary collision and reverse if needed
    const shouldReverse =
      (this.horizontalDirection > 0 &&
        this.x >= this.worldWidth - this.BOUNDARY_MARGIN) ||
      (this.horizontalDirection < 0 && this.x <= this.BOUNDARY_MARGIN);

    if (shouldReverse) {
      this.horizontalDirection *= -1;
    }

    // Update velocity with current direction
    const scaleFactor = ResolutionManager.getInstance().getScaleFactor();
    const globalDownwardSpeed =
      GameConfig.getInstance().getGlobalDownwardSpeed() * scaleFactor;
    const baseSpeed = this.speed || globalDownwardSpeed;
    const horizontalSpeed = baseSpeed * this.horizontalDirection;
    const verticalSpeed = globalDownwardSpeed; // Same downward speed as Enemy1
    this.setVelocity(horizontalSpeed, verticalSpeed);

    // Update rotation to face movement direction
    this.rotation = this.horizontalDirection > 0 ? Math.PI / 2 : -Math.PI / 2;
  }

  protected updateShooting(
    time: number,
    playerPosition?: { x: number; y: number }
  ): void {
    if (!playerPosition || !this.bulletManager) return;

    if (time > this.nextFireTime) {
      // Shoot in the direction the enemy is facing (horizontally)
      const direction = this.rotation;
      this.shoot(direction);
      this.nextFireTime = time + this.fireRate;
    }
  }
}

export { Enemy2 };
