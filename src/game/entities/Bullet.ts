import { GameConfig } from "../config/GameConfig";
import { ResolutionManager } from "../systems/ResolutionManager";

export enum BulletType {
  Player = "player",
  Enemy = "enemy",
}

class Bullet extends Phaser.Physics.Matter.Sprite {
  // Bullet type
  public bulletType: BulletType = BulletType.Player; // Default to player

  // World bounds (using target resolution)
  private WORLD_WIDTH: number;
  private WORLD_HEIGHT: number;
  private BULLET_BUFFER: number;

  // Resolution management
  private gameConfig: GameConfig;
  private resolutionManager: ResolutionManager;

  // Constants
  private readonly BULLET_SCALE: number = 0.1; // Scale to maintain current size

  constructor(
    scene: Phaser.Scene,
    x: number = 0,
    y: number = 0,
    texture: string,
    frame?: number | string
  ) {
    super(scene.matter.world, x, y, texture || "bullet", frame);

    // Initialize resolution management
    this.gameConfig = GameConfig.getInstance();
    this.resolutionManager = ResolutionManager.getInstance();

    // Get world constants from target resolution
    const targetRes = this.resolutionManager.getTargetResolution();
    this.WORLD_WIDTH = targetRes.width;
    this.WORLD_HEIGHT = targetRes.height;
    this.BULLET_BUFFER = 50 * this.resolutionManager.getScaleFactor();

    // Create bullet visuals
    this.createBulletVisuals();
  }

  private setupPhysicsBody(): void {
    // Set collision bounds - small rectangle for bullet
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const bodySize = 4 * scaleFactor;
    this.setRectangle(bodySize, bodySize);

    // Configure physics properties - no friction for fast bullets
    this.setBounce(0); // No bounce
    this.setIgnoreGravity(true); // Ignore world gravity
    this.setFrictionAir(0); // No air resistance for bullets

    // Set collision categories based on bullet type
    const playerCategory = 0x0001;
    const enemyCategory = 0x0002;
    const playerBulletCategory = 0x0003;
    const enemyBulletCategory = 0x0004;
    const moduleCategory = 0x0005;

    if (this.bulletType === BulletType.Player) {
      this.setCollisionCategory(playerBulletCategory);
      this.setCollidesWith([enemyCategory]);
    } else {
      this.setCollisionCategory(enemyBulletCategory);
      this.setCollidesWith([playerCategory, moduleCategory]);
    }
  }

  private createBulletVisuals(): void {
    // Set up the sprite with scaling based on resolution
    const scaleFactor = this.resolutionManager.getScaleFactor();
    this.setScale(this.BULLET_SCALE * scaleFactor);

    // Set tint to yellow for bullets
    this.setTint(0xffff00);
  }

  // Initialize method called when a bullet is fired (pooling system)
  public fire(
    startX: number,
    startY: number,
    rotation: number,
    type: BulletType
  ): void {
    // Reset state for pooling
    this.setActive(true);
    this.setVisible(true);

    // Set position
    this.x = startX;
    this.y = startY;

    // Set type and calculate speed (scaled for resolution)
    this.bulletType = type;
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const baseBulletSpeed = this.bulletType === BulletType.Player ? 5 : 4;
    const bulletSpeed = baseBulletSpeed * scaleFactor;

    // Configure physics body
    this.setupPhysicsBody();

    // Set velocity based on rotation
    const velocityX = Math.sin(rotation) * bulletSpeed;
    const velocityY = -Math.cos(rotation) * bulletSpeed;
    this.setVelocity(velocityX, velocityY);
  }

  public update(_time: number, delta: number): void {
    // Physics handles movement automatically, no manual updates needed
  }

  public isOffScreen(): boolean {
    return (
      this.x < -this.BULLET_BUFFER ||
      this.x > this.WORLD_WIDTH + this.BULLET_BUFFER ||
      this.y < -this.BULLET_BUFFER ||
      this.y > this.WORLD_HEIGHT + this.BULLET_BUFFER
    );
  }

  public getBulletType(): BulletType {
    return this.bulletType;
  }

  public destroy(): void {
    // Call parent destroy
    super.destroy();
  }
}

export { Bullet };
