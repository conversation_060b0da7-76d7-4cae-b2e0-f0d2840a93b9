import { ResolutionManager } from "../systems/ResolutionManager";

class Teleport extends Phaser.Physics.Matter.Sprite {
  // Visual properties
  private readonly TELEPORT_SIZE: number = 50; // 50x50 zone
  private readonly PULSE_SPEED: number = 2000; // 2 second pulse cycle
  private readonly MIN_ALPHA: number = 0.3;
  private readonly MAX_ALPHA: number = 0.8;

  // Resolution management
  private resolutionManager: ResolutionManager;

  // Visual elements
  private outerCircle!: Phaser.GameObjects.Graphics;
  private pulseStartTime: number = 0;

  // Interaction state
  private playerInZone: boolean = false;

  constructor(scene: Phaser.Scene, x: number, y: number) {
    super(scene.matter.world, x, y, "");

    // Initialize resolution management
    this.resolutionManager = ResolutionManager.getInstance();

    // Create visual elements
    this.createVisuals();

    // Setup physics body
    this.setupPhysicsBody();

    // Add to scene
    scene.add.existing(this);

    // Start pulse animation
    this.pulseStartTime = scene.time.now;
  }

  private createVisuals(): void {
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const scaledSize = this.TELEPORT_SIZE * scaleFactor;

    // Create outer circle (larger, more transparent)
    this.outerCircle = this.scene.add.graphics();

    // Initialize with normal state
    this.updateVisualState(false);

    // Make this sprite invisible since we're using graphics for visuals
    this.setVisible(false);
  }

  private updateVisualState(playerInZone: boolean): void {
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const scaledSize = this.TELEPORT_SIZE * scaleFactor;

    // Clear existing graphics
    this.outerCircle.clear();

    // Choose colors based on player presence
    const outerColor = playerInZone ? 0xffff00 : 0x00aa00; // Brighter when player is in zone

    // Draw outer circle
    this.outerCircle.lineStyle(2 * scaleFactor, outerColor, 0.6);
    this.outerCircle.fillStyle(outerColor, 0.1);
    this.outerCircle.fillCircle(0, 0, scaledSize / 2);
    this.outerCircle.strokeCircle(0, 0, scaledSize / 2);
    this.outerCircle.x = this.x;
    this.outerCircle.y = this.y;
  }

  private setupPhysicsBody(): void {
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const scaledSize = this.TELEPORT_SIZE * scaleFactor;

    // Set collision bounds as a circle
    this.setCircle(scaledSize / 2);

    // Configure physics properties
    this.setBounce(0);
    this.setIgnoreGravity(true);
    this.setStatic(true); // Teleport doesn't move
    this.setSensor(true); // Allow objects to pass through (trigger only)

    // Set collision category for teleport
    const teleportCategory = 0x0006;
    const playerCategory = 0x0001;
    this.setCollisionCategory(teleportCategory);
    this.setCollidesWith([playerCategory]);
  }

  public update(time: number, delta: number): void {
    // Update pulse animation
    this.updatePulseAnimation(time);

    // Update visual positions to follow the physics body
    this.outerCircle.x = this.x;
    this.outerCircle.y = this.y;
  }

  private updatePulseAnimation(time: number): void {
    // Calculate pulse progress (0 to 1)
    const elapsed = time - this.pulseStartTime;
    const progress = (elapsed % this.PULSE_SPEED) / this.PULSE_SPEED;

    // Create a smooth pulse using sine wave
    const pulseValue = Math.sin(progress * Math.PI * 2) * 0.5 + 0.5;

    // Calculate alpha values
    const outerAlpha =
      this.MIN_ALPHA + (this.MAX_ALPHA - this.MIN_ALPHA) * pulseValue * 0.5;
    const innerAlpha =
      this.MIN_ALPHA + (this.MAX_ALPHA - this.MIN_ALPHA) * pulseValue;

    // Apply alpha to circles
    this.outerCircle.setAlpha(outerAlpha);
  }

  public onPlayerEnter(): void {
    this.playerInZone = true;
    // Make the teleport slightly brighter when player is in zone
    // Graphics objects don't use setTint, we'll recreate them with brighter colors
    this.updateVisualState(true);
  }

  public onPlayerExit(): void {
    this.playerInZone = false;
    // Reset to normal color
    this.updateVisualState(false);
  }

  public isPlayerInZone(): boolean {
    return this.playerInZone;
  }

  public activate(): void {
    // This method will be called when the player presses the action button while in the zone
    console.log("Teleport activated! Switching to Level scene...");
  }

  public destroy(): void {
    // Clean up graphics
    if (this.outerCircle) {
      this.outerCircle.destroy();
    }

    // Call parent destroy
    super.destroy();
  }
}

export { Teleport };
