import { Player } from "./Player";
import { InputSystem } from "../systems/InputSystem";
import { BulletManager } from "../systems/BulletManager";
import { BulletType } from "./Bullet";
import { emit } from "../systems/EventBus";

class Module extends Phaser.Physics.Matter.Sprite {
  private player: Player;
  private moduleGraphics: Phaser.GameObjects.Graphics;
  private inputSystem: InputSystem;
  private bulletManager: BulletManager | null = null;
  private lastFired: number = 0;
  private readonly fireRate: number = 200;
  private readonly MODULE_SCALE: number = 1.2;
  private offsetX: number;
  private offsetY: number;
  private delayFrames: number = 1;
  private positionHistory: { x: number; y: number; rotation: number }[] = [];

  constructor(
    scene: Phaser.Scene,
    player: Player,
    offsetX: number = 0,
    offsetY: number = 0,
    texture?: string,
    frame?: number | string
  ) {
    super(scene.matter.world, 0, 0, texture || "__DEFAULT", frame);
    this.player = player;
    this.offsetX = offsetX;
    this.offsetY = offsetY;
    this.inputSystem = InputSystem.getInstance();
    this.createModuleVisuals();

    // Add to scene
    scene.add.existing(this);

    // Configure physics body
    this.setupPhysicsBody();
  }

  private setupPhysicsBody(): void {
    // Set collision bounds - diamond shape approximated with a smaller rectangle
    const bodyWidth = 14 * this.MODULE_SCALE;
    const bodyHeight = 20 * this.MODULE_SCALE;
    this.setRectangle(bodyWidth, bodyHeight);

    // Configure physics properties
    this.setBounce(0); // No bounce
    this.setIgnoreGravity(true); // Ignore world gravity
    this.setStatic(true); // Modules don't move from collisions (immovable)

    // Set collision category for module
    const moduleCategory = 0x0005;
    const enemyBulletCategory = 0x0004;
    this.setCollisionCategory(moduleCategory);
    this.setCollidesWith([enemyBulletCategory]);
  }

  private createModuleVisuals(): void {
    this.moduleGraphics = this.scene.add.graphics();
    this.moduleGraphics.fillStyle(0xffff00, 1);
    this.createModulePath();
    this.moduleGraphics.fillPath();
  }

  private createModulePath(): void {
    this.moduleGraphics.beginPath();
    this.moduleGraphics.moveTo(0, -10 * this.MODULE_SCALE);
    this.moduleGraphics.lineTo(7 * this.MODULE_SCALE, 0);
    this.moduleGraphics.lineTo(0, 10 * this.MODULE_SCALE);
    this.moduleGraphics.lineTo(-7 * this.MODULE_SCALE, 0);
    this.moduleGraphics.closePath();
  }

  update(time: number, delta: number): void {
    this.updatePositionAndRotation();
    this.handleFiring(time);
    this.moduleGraphics.x = this.x;
    this.moduleGraphics.y = this.y;
    this.moduleGraphics.rotation = this.rotation;
  }

  private updatePositionAndRotation(): void {
    const playerPos = this.player.getPosition();
    const playerRot = this.player.getRotation();
    this.positionHistory.push({
      x: playerPos.x,
      y: playerPos.y,
      rotation: playerRot,
    });
    if (this.positionHistory.length > this.delayFrames + 1) {
      this.positionHistory.shift();
    }
    const delayedState = this.positionHistory[0] || {
      x: playerPos.x,
      y: playerPos.y,
      rotation: playerRot,
    };
    const cos = Math.cos(delayedState.rotation);
    const sin = Math.sin(delayedState.rotation);
    const worldOffsetX = this.offsetX * cos - this.offsetY * sin;
    const worldOffsetY = this.offsetX * sin + this.offsetY * cos;
    this.x = delayedState.x + worldOffsetX;
    this.y = delayedState.y + worldOffsetY;
    this.rotation = delayedState.rotation;
  }

  private handleFiring(time: number): void {
    const firePressed = this.inputSystem.isDown("action1");
    if (
      firePressed &&
      time > this.lastFired + this.fireRate &&
      this.bulletManager
    ) {
      this.fireBullet();
      this.lastFired = time;
    }
  }

  private fireBullet(): void {
    if (!this.bulletManager) return;
    const tipOffset = 10 * this.MODULE_SCALE;
    const startX = this.x + Math.sin(this.rotation) * tipOffset;
    const startY = this.y - Math.cos(this.rotation) * tipOffset;
    this.bulletManager.fireBullet(startX, startY, 0, BulletType.Player);
  }

  public setBulletManager(bulletManager: BulletManager): void {
    this.bulletManager = bulletManager;
  }

  public destroy(): void {
    emit("module:destroyed", { module: this });
    if (this.moduleGraphics) {
      this.moduleGraphics.destroy();
    }
    super.destroy();
  }
}

export { Module };
