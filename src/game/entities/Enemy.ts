import { BulletManager } from "../systems/BulletManager";
import { ParticleManager } from "../systems/ParticleManager";
import { BulletType } from "./Bullet";
import { GameConfig } from "../config/GameConfig";
import { ResolutionManager } from "../systems/ResolutionManager";

export interface EnemyConfig {
  maxHealth?: number;
  color?: number;
  size?: number;
  speed?: number;
  turnSpeed?: number;
  fireRate?: number;
  shootInFacingDirection?: boolean;
  scoreValue?: number;
}

class Enemy extends Phaser.Physics.Matter.Sprite {
  // World bounds (using virtual coordinates)
  private WORLD_WIDTH: number;
  private WORLD_HEIGHT: number;
  private REMOVE_DISTANCE: number;

  // Resolution management
  private gameConfig: GameConfig;
  private resolutionManager: ResolutionManager;

  // Configuration
  private config: Required<EnemyConfig>;

  // Health system
  private maxHealth: number;
  private currentHealth: number;
  private isFlashing: boolean = false;
  private readonly flashDuration: number = 300; // milliseconds
  private flashStartTime: number = 0;
  private flashIntensity: number = 0; // 0 = normal color, 1 = full white

  // Movement properties
  protected speed: number;
  protected turnSpeed: number;

  // Shooting properties
  protected lastFired: number = 0;
  protected fireRate: number;
  protected shootInFacingDirection: boolean;
  protected bulletManager: BulletManager | null = null;
  protected particleManager: ParticleManager | null = null;
  protected nextFireTime: number = 0;

  // Constants
  private readonly ENEMY_SCALE: number = 0.5;

  // Visual
  private enemyGraphics: Phaser.GameObjects.Graphics;

  constructor(
    scene: Phaser.Scene,
    x?: number,
    y?: number,
    config: EnemyConfig = {},
    bulletManager: BulletManager | null = null,
    texture?: string,
    frame?: number | string
  ) {
    super(scene.matter.world, x ?? 0, y ?? 0, texture || "enemy", frame);

    // Store bullet manager reference
    this.bulletManager = bulletManager;

    // Initialize resolution management
    this.gameConfig = GameConfig.getInstance();
    this.resolutionManager = ResolutionManager.getInstance();

    // Get world constants from target resolution (since enemies work in screen coordinates)
    const targetRes = this.resolutionManager.getTargetResolution();
    this.WORLD_WIDTH = targetRes.width;
    this.WORLD_HEIGHT = targetRes.height;
    this.REMOVE_DISTANCE = 50 * this.resolutionManager.getScaleFactor(); // Scaled for target resolution

    // Set configuration with defaults (speed will be scaled)
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const globalDownwardSpeed = this.gameConfig.getGlobalDownwardSpeed();
    this.config = {
      maxHealth: 1,
      color: 0xff0000,
      size: 0.8,
      speed: globalDownwardSpeed * scaleFactor, // Use global downward speed
      turnSpeed: 0.25,
      fireRate: 1500,
      shootInFacingDirection: true,
      scoreValue: 100,
      ...config,
    };

    // Initialize health system
    this.maxHealth = this.config.maxHealth;
    this.currentHealth = this.maxHealth;

    // Initialize movement properties
    this.speed = this.config.speed;
    this.turnSpeed = this.config.turnSpeed;

    // Initialize shooting properties
    this.fireRate = this.config.fireRate;
    this.shootInFacingDirection = this.config.shootInFacingDirection;
    this.nextFireTime = scene.time.now + Phaser.Math.Between(1000, 4000);

    // Create visual representation
    this.createVisual();

    // Position enemy
    this.x = x ?? 0;
    this.y = y ?? 0;

    // Add to scene
    scene.add.existing(this);

    // Configure physics body
    this.setupPhysicsBody();

    // Initialize movement (after physics body is created)
    this.initializeMovement();
  }

  private setupPhysicsBody(): void {
    // Set collision bounds - ship shape approximated with a rectangle
    const scaleFactor = this.resolutionManager.getScaleFactor();
    const bodyWidth = 20 * scaleFactor;
    const bodyHeight = 24 * scaleFactor;
    this.setRectangle(bodyWidth, bodyHeight);

    // Configure physics properties
    this.setBounce(0); // No bounce
    this.setIgnoreGravity(true); // Ignore world gravity
    this.setFrictionAir(0); // No air resistance for enemies

    // Set collision category for enemy - enemies should only collide with player bullets
    const enemyCategory = 0x0002;
    const playerBulletCategory = 0x0003;
    this.setCollisionCategory(enemyCategory);
    this.setCollidesWith([playerBulletCategory]); // Only collide with player bullets, not other enemies
    this.setCollisionGroup(-1); // Negative group means they don't collide with each other
  }

  private createVisual(): void {
    // Set up the sprite with scaling based on resolution
    const scaleFactor = this.resolutionManager.getScaleFactor();
    this.setScale(this.config.size * 0.15 * scaleFactor);

    // Set tint based on enemy color
    // this.setTint(this.config.color);

    // FIXME: Disable blur for now, it's too slow
    // this.enableFilters();
    // this.filters?.external.addBlur(0, 1, 1, 1, 0xffffff, 10);
  }

  // Interpolate between two colors based on a factor (0 = color1, 1 = color2)
  private interpolateColor(
    color1: number,
    color2: number,
    factor: number
  ): number {
    // Clamp factor between 0 and 1
    factor = Math.max(0, Math.min(1, factor));

    // Extract RGB components from hex colors
    const r1 = (color1 >> 16) & 0xff;
    const g1 = (color1 >> 8) & 0xff;
    const b1 = color1 & 0xff;

    const r2 = (color2 >> 16) & 0xff;
    const g2 = (color2 >> 8) & 0xff;
    const b2 = color2 & 0xff;

    // Interpolate each component
    const r = Math.round(r1 + (r2 - r1) * factor);
    const g = Math.round(g1 + (g2 - g1) * factor);
    const b = Math.round(b1 + (b2 - b1) * factor);

    // Combine back into hex color
    return (r << 16) | (g << 8) | b;
  }

  protected initializeMovement(): void {
    // Start with a slower initial downward movement
    this.setVelocity(0, this.speed);

    // Face downward (0 rotation = facing down in this coordinate system)
    this.rotation = 0;
  }

  public update(
    time: number,
    delta: number,
    playerPosition?: { x: number; y: number }
  ): void {
    this.updateFlashing(time);
    this.updateMovement(time, delta, playerPosition);
    this.updateShooting(time, playerPosition);
  }

  private updateFlashing(time: number): void {
    if (this.isFlashing) {
      const elapsed = time - this.flashStartTime;

      if (elapsed >= this.flashDuration) {
        // Flash is complete
        this.isFlashing = false;
        this.flashIntensity = 0;
        this.setTint(this.config.color); // Reset to normal color
      } else {
        // Calculate flash intensity (1 at start, 0 at end)
        const progress = elapsed / this.flashDuration;
        this.flashIntensity = 1 - progress; // Linear fade from 1 to 0

        // Interpolate tint color
        const flashColor = this.interpolateColor(
          this.config.color,
          0xffffff,
          this.flashIntensity
        );
        this.setTint(flashColor);
      }
    }
  }

  protected updateMovement(
    time: number,
    delta: number,
    playerPosition?: { x: number; y: number }
  ): void {
    if (!playerPosition) return;

    const deltaSeconds = delta / 1000;

    // Direction to player for gradual turning behaviour
    const angleToPlayer = Math.atan2(
      playerPosition.y - this.y,
      playerPosition.x - this.x
    );

    // Current facing angle (based on current velocity)
    let currentAngle = Math.atan2(this.body!.velocity.y, this.body!.velocity.x);

    // Shortest angular distance to player
    let angleDifference = angleToPlayer - currentAngle;
    while (angleDifference > Math.PI) angleDifference -= 2 * Math.PI;
    while (angleDifference < -Math.PI) angleDifference += 2 * Math.PI;

    // Apply gradual turning
    const maxTurnThisFrame = this.turnSpeed * deltaSeconds;
    const turnAmount =
      Math.sign(angleDifference) *
      Math.min(Math.abs(angleDifference), maxTurnThisFrame);
    currentAngle += turnAmount;

    // Maintain speed, update velocity using physics
    const newVelX = Math.cos(currentAngle) * this.speed;
    const newVelY = Math.sin(currentAngle) * this.speed;
    this.setVelocity(newVelX, newVelY);

    // Update rotation to match movement
    this.rotation = Math.atan2(this.body!.velocity.x, -this.body!.velocity.y);
  }

  protected updateShooting(
    time: number,
    playerPosition?: { x: number; y: number }
  ): void {
    if (!playerPosition || !this.bulletManager) return;
    if (time > this.nextFireTime) {
      const direction = this.rotation;
      this.shoot(direction);
      this.nextFireTime = time + Phaser.Math.Between(1000, 4000);
    }
  }

  public shoot(direction: number): void {
    if (!this.bulletManager) return;

    // Calculate bullet spawn position in front of the enemy
    const tipOffset = 15 * this.config.size;
    const bulletX = this.x + Math.sin(this.rotation) * tipOffset;
    const bulletY = this.y - Math.cos(this.rotation) * tipOffset;

    this.bulletManager.fireBullet(
      bulletX,
      bulletY,
      direction,
      BulletType.Enemy
    );
  }

  public takeDamage(time: number): boolean {
    this.currentHealth--;

    if (this.currentHealth > 0) {
      // Start flash effect - immediately white, then fade back to normal
      this.isFlashing = true;
      this.flashStartTime = time;
      this.flashIntensity = 1; // Start at full white
      this.setTint(0xffffff); // Start with white tint
      return false; // Not destroyed
    } else {
      return true; // Destroyed
    }
  }

  public isOffScreen(): boolean {
    // Check if enemy is far from camera viewport with generous buffer
    const camera = this.scene.cameras.main;
    const viewportLeft = camera.worldView.x;
    const viewportTop = camera.worldView.y;
    const viewportRight = viewportLeft + camera.width;
    const viewportBottom = viewportTop + camera.height;

    // Use a larger buffer to allow enemies to move well outside the screen
    const buffer = this.REMOVE_DISTANCE * 2; // Double the buffer for more generous bounds

    // Remove enemies that are very far outside the camera view
    return (
      this.x < viewportLeft - buffer ||
      this.x > viewportRight + buffer ||
      this.y < viewportTop - buffer ||
      this.y > viewportBottom + buffer
    );
  }

  public getPosition(): { x: number; y: number } {
    return { x: this.x, y: this.y };
  }

  public getVelocity(): { x: number; y: number } {
    return {
      x: this.body!.velocity.x,
      y: this.body!.velocity.y,
    };
  }

  public getScoreValue(): number {
    return this.config.scoreValue;
  }

  public setBulletManager(bulletManager: BulletManager): void {
    this.bulletManager = bulletManager;
  }

  public setParticleManager(particleManager: ParticleManager): void {
    this.particleManager = particleManager;
  }

  public explode(): void {
    if (this.particleManager) {
      this.particleManager.createExplosion(this.x, this.y);
    }
  }

  public destroy(): void {
    // Call parent destroy
    super.destroy();
  }

  // Static method to spawn enemies at given coordinates
  public static spawnEnemy(
    scene: Phaser.Scene,
    x: number,
    y: number,
    config: EnemyConfig = {},
    bulletManager: BulletManager | null = null
  ): Enemy {
    return new Enemy(scene, x, y, config, bulletManager);
  }
}

export { Enemy };
