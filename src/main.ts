import StartGame from "./game/main";

document.addEventListener("DOMContentLoaded", () => {
  const game = StartGame("game-container");

  // Remember the last scene before page reload during HMR
  if (import.meta.hot) {
    import.meta.hot.on("vite:beforeFullReload", () => {
      const activeScenes = game.scene.getScenes(true);
      if (activeScenes.length > 0) {
        const currentScene = activeScenes[0];
        sessionStorage.setItem("lastScene", currentScene.sys.settings.key);
      }
    });
  }
});
