{"name": "template-bun", "description": "A Phaser 3 template using bun.", "version": "1.1.0", "repository": {"type": "git", "url": "git+https://github.com/phaserjs/template-bun.git"}, "author": "Phaser Studio <<EMAIL>> (https://phaser.io/)", "license": "MIT", "licenseUrl": "http://www.opensource.org/licenses/mit-license.php", "bugs": {"url": "https://github.com/phaserjs/template-bun/issues"}, "homepage": "https://github.com/phaserjs/template-bun#readme", "scripts": {"dev": "vite --config vite/config.dev.mjs", "build": "vite build --config vite/config.prod.mjs"}, "devDependencies": {"terser": "^5.39.0", "typescript": "~5.7.3", "vite": "^6.3.1"}, "dependencies": {"phaser": "4.0.0-rc.4", "phaser-box2d": "^1.1.0"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977"}